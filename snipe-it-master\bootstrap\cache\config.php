<?php return array (
  4 => 'concurrency',
  'app' => 
  array (
    'name' => 'Snipe-IT',
    'env' => 'production',
    'debug' => true,
    'url' => 'http://127.0.0.1:8000',
    'frontend_url' => 'http://localhost:3000',
    'asset_url' => NULL,
    'timezone' => 'UTC',
    'locale' => 'en-US',
    'fallback_locale' => 'en-US',
    'faker_locale' => 'en_US',
    'cipher' => 'AES-256-CBC',
    'key' => 'base64:rDjld9x5ketq7X0TbCL2SP2D7MVIZ90ztM4tMe6pA+c=',
    'previous_keys' => 
    array (
    ),
    'maintenance' => 
    array (
      'driver' => 'file',
      'store' => 'database',
    ),
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Cookie\\CookieServiceProvider',
      6 => 'Illuminate\\Database\\DatabaseServiceProvider',
      7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      10 => 'Illuminate\\Hashing\\HashServiceProvider',
      11 => 'Illuminate\\Mail\\MailServiceProvider',
      12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      15 => 'Illuminate\\Queue\\QueueServiceProvider',
      16 => 'Illuminate\\Redis\\RedisServiceProvider',
      17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      18 => 'Illuminate\\Session\\SessionServiceProvider',
      19 => 'App\\Providers\\SnipeTranslationServiceProvider',
      20 => 'Illuminate\\Validation\\ValidationServiceProvider',
      21 => 'Illuminate\\View\\ViewServiceProvider',
      22 => 'Barryvdh\\DomPDF\\ServiceProvider',
      23 => 'Intervention\\Image\\ImageServiceProvider',
      24 => 'Collective\\Html\\HtmlServiceProvider',
      25 => 'Spatie\\Backup\\BackupServiceProvider',
      26 => 'PragmaRX\\Google2FALaravel\\ServiceProvider',
      27 => 'Laravel\\Passport\\PassportServiceProvider',
      28 => 'Laravel\\Tinker\\TinkerServiceProvider',
      29 => 'Unicodeveloper\\DumbPassword\\DumbPasswordServiceProvider',
      30 => 'Eduardokum\\LaravelMailAutoEmbed\\ServiceProvider',
      31 => 'Laravel\\Socialite\\SocialiteServiceProvider',
      32 => 'App\\Providers\\AppServiceProvider',
      33 => 'App\\Providers\\AuthServiceProvider',
      34 => 'App\\Providers\\EventServiceProvider',
      35 => 'App\\Providers\\RouteServiceProvider',
      36 => 'App\\Providers\\SettingsServiceProvider',
      37 => 'App\\Providers\\ValidationServiceProvider',
      38 => 'App\\Providers\\BladeServiceProvider',
      39 => 'App\\Providers\\LivewireServiceProvider',
      40 => 'App\\Providers\\MacroServiceProvider',
      41 => 'App\\Providers\\SamlServiceProvider',
      42 => 'App\\Providers\\BreadcrumbsServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'Date' => 'Illuminate\\Support\\Facades\\Date',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'PDF' => 'Barryvdh\\DomPDF\\Facade',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Redis' => 'Illuminate\\Support\\Facades\\Redis',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Form' => 'Collective\\Html\\FormFacade',
      'Html' => 'Collective\\Html\\HtmlFacade',
      'Google2FA' => 'PragmaRX\\Google2FALaravel\\Facade',
      'Image' => 'Intervention\\Image\\ImageServiceProvider',
      'Carbon' => 'Carbon\\Carbon',
      'Helper' => 'App\\Helpers\\Helper',
      'StorageHelper' => 'App\\Helpers\\StorageHelper',
      'Icon' => 'App\\Helpers\\IconHelper',
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
    'max_results' => '500',
    'warn_debug' => true,
    'private_uploads' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage/private_uploads',
    'allow_iframing' => false,
    'enable_hsts' => false,
    'referrer_policy' => 'same-origin',
    'enable_csp' => false,
    'additional_csp_urls' => NULL,
    'require_saml' => false,
    'lock_passwords' => false,
    'min_php' => '8.2.0',
    'api_throttle_per_minute' => '120',
    'allow_purge' => false,
    'allow_backup_delete' => false,
    'escape_formulas' => true,
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'api' => 
      array (
        'driver' => 'passport',
        'provider' => 'users',
        'hash' => false,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\Models\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'email' => 'auth.emails.password',
        'table' => 'password_resets',
        'expire' => '15',
        'throttle' => 
        array (
          'max_attempts' => '5',
          'lockout_duration' => '60',
        ),
      ),
      'invites' => 
      array (
        'provider' => 'users',
        'table' => 'password_resets',
        'expire' => '1500',
        'throttle' => 
        array (
          'max_attempts' => '5',
          'lockout_duration' => '60',
        ),
      ),
    ),
    'password_timeout' => '10800',
    'password_reset' => 
    array (
      'max_attempts_per_min' => '50',
    ),
    'login_autocomplete' => false,
  ),
  'backup' => 
  array (
    'backup' => 
    array (
      'name' => 'backups',
      'source' => 
      array (
        'files' => 
        array (
          'relative_path' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master',
          'include' => 
          array (
            0 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\public/uploads',
            1 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage/private_uploads',
            2 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage/oauth-private.key',
            3 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage/oauth-public.key',
            4 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\.env',
          ),
          'exclude' => 
          array (
            0 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor',
            1 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\config',
            2 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\node_modules',
          ),
          'follow_links' => false,
          'ignore_unreadable_directories' => false,
        ),
        'databases' => 
        array (
          0 => 'mysql',
        ),
      ),
      'database_dump_compressor' => NULL,
      'destination' => 
      array (
        'filename_prefix' => 'snipe-it-',
        'disks' => 
        array (
          0 => 'backup',
        ),
      ),
      'temporary_directory' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\app/backup-temp',
      'encryption' => NULL,
    ),
    'notifications' => 
    array (
      'notifications' => 
      array (
        'Spatie\\Backup\\Notifications\\Notifications\\BackupHasFailedNotification' => 
        array (
          0 => NULL,
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\UnhealthyBackupWasFoundNotification' => 
        array (
          0 => NULL,
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\CleanupHasFailedNotification' => 
        array (
          0 => NULL,
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\BackupWasSuccessfulNotification' => 
        array (
          0 => NULL,
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\HealthyBackupWasFoundNotification' => 
        array (
          0 => NULL,
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\CleanupWasSuccessfulNotification' => 
        array (
          0 => NULL,
        ),
      ),
      'notifiable' => 'Spatie\\Backup\\Notifications\\Notifiable',
      'mail' => 
      array (
        'to' => NULL,
        'from' => 
        array (
          'address' => '<EMAIL>',
          'name' => 'Snipe-IT',
        ),
      ),
      'slack' => 
      array (
        'webhook_url' => '',
        'channel' => NULL,
        'username' => NULL,
        'icon' => NULL,
      ),
      'discord' => 
      array (
        'webhook_url' => '',
        'username' => '',
        'avatar_url' => '',
      ),
    ),
    'monitor_backups' => 
    array (
      0 => 
      array (
        'name' => 'Snipe-IT',
        'disks' => 
        array (
          0 => 'local',
        ),
        'health_checks' => 
        array (
          'Spatie\\Backup\\Tasks\\Monitor\\HealthChecks\\MaximumAgeInDays' => 1,
          'Spatie\\Backup\\Tasks\\Monitor\\HealthChecks\\MaximumStorageInMegabytes' => 5000,
        ),
      ),
    ),
    'cleanup' => 
    array (
      'strategy' => 'Spatie\\Backup\\Tasks\\Cleanup\\Strategies\\DefaultStrategy',
      'default_strategy' => 
      array (
        'keep_all_backups_for_days' => '7',
        'keep_daily_backups_for_days' => '16',
        'keep_weekly_backups_for_weeks' => '8',
        'keep_monthly_backups_for_months' => '4',
        'keep_yearly_backups_for_years' => '2',
        'delete_oldest_backups_when_using_more_megabytes_than' => '5000',
      ),
    ),
    'sanitize_by_default' => false,
  ),
  'broadcasting' => 
  array (
    'default' => 'null',
    'connections' => 
    array (
      'reverb' => 
      array (
        'driver' => 'reverb',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'host' => NULL,
          'port' => 443,
          'scheme' => 'https',
          'useTLS' => true,
        ),
        'client_options' => 
        array (
        ),
      ),
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => NULL,
        'secret' => NULL,
        'app_id' => NULL,
        'options' => 
        array (
          'cluster' => NULL,
          'useTLS' => true,
        ),
      ),
      'ably' => 
      array (
        'driver' => 'ably',
        'key' => NULL,
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'array' => 
      array (
        'driver' => 'array',
        'serialize' => false,
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'cache',
        'connection' => NULL,
        'lock_connection' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\framework/cache',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => NULL,
            'port' => NULL,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'lock_connection' => 'default',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => NULL,
        'secret' => NULL,
        'region' => NULL,
        'table' => 'cache',
        'endpoint' => NULL,
      ),
      'octane' => 
      array (
        'driver' => 'octane',
      ),
      'apc' => 
      array (
        'driver' => 'apc',
      ),
    ),
    'prefix' => 'snipeit',
  ),
  'compile' => 
  array (
    'files' => 
    array (
    ),
    'providers' => 
    array (
    ),
  ),
  'cors' => 
  array (
    'paths' => 
    array (
      0 => 'api/*',
      1 => 'sanctum/csrf-cookie',
    ),
    'allowed_methods' => 
    array (
      0 => 'GET',
      1 => 'POST',
      2 => 'PUT',
      3 => 'PATCH',
      4 => 'DELETE',
    ),
    'allowed_origins' => 
    array (
    ),
    'allowed_origins_patterns' => 
    array (
    ),
    'allowed_headers' => 
    array (
      0 => '*',
    ),
    'exposed_headers' => 
    array (
    ),
    'max_age' => 0,
    'supports_credentials' => false,
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'database' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\database\\database.sqlite',
        'prefix' => '',
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'host' => '127.0.0.1',
        'port' => '4306',
        'database' => 'snipeit',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => NULL,
        'strict' => false,
        'engine' => 'InnoDB',
        'unix_socket' => NULL,
        'dump' => 
        array (
          'dump_binary_path' => '/usr/bin',
          'use_single_transaction' => false,
          'timeout' => 300,
        ),
        'dump_command_timeout' => 300,
        'dump_using_single_transaction' => true,
        'options' => 
        array (
        ),
      ),
      'mariadb' => 
      array (
        'driver' => 'mariadb',
        'url' => NULL,
        'host' => '127.0.0.1',
        'port' => '4306',
        'database' => 'snipeit',
        'username' => 'root',
        'password' => '',
        'unix_socket' => NULL,
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'host' => '127.0.0.1',
        'port' => '4306',
        'database' => 'snipeit',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'schema' => 'public',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'host' => '127.0.0.1',
        'database' => 'snipeit',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
      ),
      'sqlite_testing' => 
      array (
        'driver' => 'sqlite',
        'database' => ':memory:',
        'prefix' => '',
      ),
    ),
    'migrations' => 'migrations',
    'redis' => 
    array (
      'cluster' => false,
      'default' => 
      array (
        'host' => NULL,
        'password' => NULL,
        'port' => NULL,
        'database' => 0,
      ),
    ),
    'fetch' => 8,
  ),
  'debugbar' => 
  array (
    'enabled' => NULL,
    'hide_empty_tabs' => true,
    'except' => 
    array (
      0 => 'telescope*',
      1 => 'horizon*',
    ),
    'storage' => 
    array (
      'enabled' => true,
      'driver' => 'file',
      'path' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage/debugbar',
      'connection' => NULL,
    ),
    'editor' => 'phpstorm',
    'remote_sites_path' => NULL,
    'local_sites_path' => NULL,
    'include_vendors' => true,
    'capture_ajax' => true,
    'add_ajax_timing' => false,
    'ajax_handler_auto_show' => true,
    'ajax_handler_enable_tab' => true,
    'defer_datasets' => false,
    'error_handler' => false,
    'clockwork' => true,
    'collectors' => 
    array (
      'phpinfo' => true,
      'messages' => true,
      'time' => true,
      'memory' => true,
      'exceptions' => true,
      'log' => true,
      'db' => true,
      'views' => true,
      'route' => true,
      'laravel' => true,
      'events' => true,
      'default_request' => false,
      'symfony_request' => true,
      'mail' => true,
      'logs' => true,
      'files' => true,
      'config' => false,
      'auth' => true,
      'gate' => true,
      'session' => true,
    ),
    'options' => 
    array (
      'auth' => 
      array (
        'show_name' => false,
      ),
      'db' => 
      array (
        'with_params' => true,
        'timeline' => true,
        'backtrace' => true,
        'explain' => 
        array (
          'enabled' => false,
          'types' => 
          array (
            0 => 'SELECT',
          ),
        ),
        'hints' => true,
      ),
      'mail' => 
      array (
        'full_log' => false,
      ),
      'views' => 
      array (
        'data' => false,
      ),
      'route' => 
      array (
        'label' => true,
      ),
      'logs' => 
      array (
        'file' => NULL,
      ),
    ),
    'inject' => true,
    'route_prefix' => '_debugbar',
    'route_middleware' => 
    array (
    ),
    'route_domain' => NULL,
    'theme' => 'auto',
    'debug_backtrace_limit' => 50,
  ),
  'dompdf' => 
  array (
    'show_warnings' => false,
    'public_path' => NULL,
    'convert_entities' => false,
    'options' => 
    array (
      'font_dir' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\fonts',
      'font_cache' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\fonts',
      'temp_dir' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'chroot' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master',
      'allowed_protocols' => 
      array (
        'file://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'http://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'https://' => 
        array (
          'rules' => 
          array (
          ),
        ),
      ),
      'log_output_file' => NULL,
      'enable_font_subsetting' => false,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_paper_orientation' => 'portrait',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => true,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => true,
    ),
    'orientation' => 'portrait',
    'defines' => 
    array (
      'font_dir' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\fonts',
      'font_cache' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\fonts',
      'temp_dir' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'chroot' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master,/var/lib/snipeit/data/uploads,/var/lib/snipeit/data/private_uploads',
      'enable_font_subsetting' => true,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => true,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => false,
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage',
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\public\\uploads',
        'url' => 'http://127.0.0.1:8000/uploads',
        'visibility' => 'public',
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => NULL,
        'secret' => NULL,
        'region' => NULL,
        'bucket' => NULL,
        'url' => NULL,
        'endpoint' => NULL,
        'use_path_style_endpoint' => false,
        'throw' => false,
        'report' => false,
      ),
      'local_public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\public\\uploads',
        'url' => 'http://127.0.0.1:8000/uploads',
        'visibility' => 'public',
      ),
      's3_public' => 
      array (
        'driver' => 's3',
        'key' => NULL,
        'secret' => NULL,
        'region' => NULL,
        'bucket' => NULL,
        'url' => NULL,
        'root' => NULL,
        'visibility' => 'public',
      ),
      's3_private' => 
      array (
        'driver' => 's3',
        'key' => NULL,
        'secret' => NULL,
        'region' => NULL,
        'bucket' => NULL,
        'url' => NULL,
        'root' => NULL,
        'visibility' => 'private',
      ),
      'rackspace' => 
      array (
        'driver' => 'rackspace',
        'username' => NULL,
        'key' => NULL,
        'container' => NULL,
        'endpoint' => 'https://identity.api.rackspacecloud.com/v2.0/',
        'region' => NULL,
        'url_type' => NULL,
      ),
      'backup' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\app',
      ),
    ),
    'links' => 
    array (
      'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\public\\storage' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\app/public',
    ),
    'cloud' => 's3',
    'allowed_upload_extensions_array' => 
    array (
      0 => 'avif',
      1 => 'doc',
      2 => 'doc',
      3 => 'docx',
      4 => 'docx',
      5 => 'gif',
      6 => 'ico',
      7 => 'jpeg',
      8 => 'jpg',
      9 => 'json',
      10 => 'key',
      11 => 'lic',
      12 => 'mov',
      13 => 'mp3',
      14 => 'mp4',
      15 => 'ogg',
      16 => 'pdf',
      17 => 'png',
      18 => 'rar',
      19 => 'rtf',
      20 => 'svg',
      21 => 'txt',
      22 => 'wav',
      23 => 'webm',
      24 => 'webp',
      25 => 'xls',
      26 => 'xlsx',
      27 => 'xml',
      28 => 'zip',
    ),
    'allowed_upload_mimetypes_array' => 
    array (
      0 => 'application/json',
      1 => 'application/msword',
      2 => 'application/pdf',
      3 => 'application/vnd.ms-excel',
      4 => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      5 => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      6 => 'application/x-rar-compressed',
      7 => 'application/zip',
      8 => 'audio/*',
      9 => 'image/*',
      10 => 'text/plain',
      11 => 'text/rtf',
      12 => 'text/xml',
      13 => 'video/*',
    ),
    'allowed_upload_mimetypes' => 'application/json,application/msword,application/pdf,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/x-rar-compressed,application/zip,audio/*,image/*,text/plain,text/rtf,text/xml,video/*',
    'allowed_upload_extensions_for_validator' => 'avif,doc,doc,docx,docx,gif,ico,jpeg,jpg,json,key,lic,mov,mp3,mp4,ogg,pdf,png,rar,rtf,svg,txt,wav,webm,webp,xls,xlsx,xml,zip',
    'allowed_upload_extensions' => '.avif, .doc, .doc, .docx, .docx, .gif, .ico, .jpeg, .jpg, .json, .key, .lic, .mov, .mp3, .mp4, .ogg, .pdf, .png, .rar, .rtf, .svg, .txt, .wav, .webm, .webp, .xls, .xlsx, .xml, .zip',
  ),
  'google2fa' => 
  array (
    'enabled' => true,
    'lifetime' => 0,
    'keep_alive' => true,
    'auth' => 'auth',
    'guard' => '',
    'session_var' => 'google2fa',
    'otp_input' => 'one_time_password',
    'window' => 1,
    'forbid_old_passwords' => false,
    'otp_secret_column' => 'google2fa_secret',
    'view' => 'google2fa.index',
    'error_messages' => 
    array (
      'wrong_otp' => 'The \'One Time Password\' typed was wrong.',
      'cannot_be_empty' => 'One Time Password cannot be empty.',
      'unknown' => 'An unknown error has occurred. Please try again.',
    ),
    'throw_exceptions' => true,
    'qrcode_image_backend' => 'svg',
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => '10',
    ),
    'argon' => 
    array (
      'memory' => '1024',
      'threads' => '2',
      'time' => '2',
    ),
    'rehash_on_login' => true,
  ),
  'image' => 
  array (
    'driver' => 'gd',
  ),
  'insights' => 
  array (
    'preset' => 'laravel',
    'ide' => NULL,
    'exclude' => 
    array (
    ),
    'add' => 
    array (
      'NunoMaduro\\PhpInsights\\Domain\\Metrics\\Architecture\\Classes' => 
      array (
        0 => 'NunoMaduro\\PhpInsights\\Domain\\Insights\\ForbiddenFinalClasses',
      ),
    ),
    'remove' => 
    array (
      0 => 'SlevomatCodingStandard\\Sniffs\\Namespaces\\AlphabeticallySortedUsesSniff',
      1 => 'SlevomatCodingStandard\\Sniffs\\TypeHints\\DeclareStrictTypesSniff',
      2 => 'SlevomatCodingStandard\\Sniffs\\TypeHints\\DisallowMixedTypeHintSniff',
      3 => 'NunoMaduro\\PhpInsights\\Domain\\Insights\\ForbiddenDefineFunctions',
      4 => 'NunoMaduro\\PhpInsights\\Domain\\Insights\\ForbiddenNormalClasses',
      5 => 'NunoMaduro\\PhpInsights\\Domain\\Insights\\ForbiddenTraits',
      6 => 'SlevomatCodingStandard\\Sniffs\\TypeHints\\ParameterTypeHintSniff',
      7 => 'SlevomatCodingStandard\\Sniffs\\TypeHints\\PropertyTypeHintSniff',
      8 => 'SlevomatCodingStandard\\Sniffs\\TypeHints\\ReturnTypeHintSniff',
      9 => 'SlevomatCodingStandard\\Sniffs\\Commenting\\UselessFunctionDocCommentSniff',
    ),
    'config' => 
    array (
      'NunoMaduro\\PhpInsights\\Domain\\Insights\\ForbiddenPrivateMethods' => 
      array (
        'title' => 'The usage of private methods is not idiomatic in Laravel.',
      ),
    ),
    'requirements' => 
    array (
    ),
    'threads' => NULL,
  ),
  'livewire' => 
  array (
    'class_namespace' => 'App\\Livewire',
    'view_path' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/livewire',
    'layout' => 'components.layouts.app',
    'lazy_placeholder' => NULL,
    'temporary_file_upload' => 
    array (
      'disk' => 'local',
      'rules' => NULL,
      'directory' => NULL,
      'middleware' => NULL,
      'preview_mimes' => 
      array (
        0 => 'png',
        1 => 'gif',
        2 => 'bmp',
        3 => 'svg',
        4 => 'wav',
        5 => 'mp4',
        6 => 'mov',
        7 => 'avi',
        8 => 'wmv',
        9 => 'mp3',
        10 => 'm4a',
        11 => 'jpg',
        12 => 'jpeg',
        13 => 'mpga',
        14 => 'webp',
        15 => 'wma',
      ),
      'max_upload_time' => 5,
      'cleanup' => true,
    ),
    'render_on_redirect' => false,
    'legacy_model_binding' => true,
    'inject_assets' => true,
    'navigate' => 
    array (
      'show_progress_bar' => true,
      'progress_bar_color' => '#2299dd',
    ),
    'inject_morph_markers' => true,
    'pagination_theme' => 'tailwind',
    'url_prefix' => NULL,
  ),
  'logging' => 
  array (
    'default' => 'single',
    'deprecations' => 
    array (
      'channel' => 'null',
      'trace' => false,
    ),
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'single',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\logs/laravel.log',
        'level' => 'warning',
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\logs/laravel.log',
        'level' => 'warning',
        'days' => '10',
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'critical',
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'warning',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'level' => 'warning',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'formatter' => NULL,
        'with' => 
        array (
          'stream' => 'php://stderr',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'warning',
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'warning',
      ),
      'null' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\NullHandler',
      ),
      'emergency' => 
      array (
        'path' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\logs/laravel.log',
      ),
      'scimtrace' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\logs/scim.log',
      ),
      'rollbar' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Rollbar\\Laravel\\MonologHandler',
        'access_token' => NULL,
        'level' => 'error',
      ),
    ),
  ),
  'mail' => 
  array (
    'default' => 'smtp',
    'mailers' => 
    array (
      'smtp' => 
      array (
        'transport' => 'smtp',
        'host' => 'email-smtp.us-west-2.amazonaws.com',
        'port' => '587',
        'username' => 'YOURUSERNAME',
        'password' => 'YOURPASSWORD',
        'timeout' => 30,
        'encryption' => 'tls',
        'verify_peer' => true,
      ),
      'ses' => 
      array (
        'transport' => 'ses',
      ),
      'postmark' => 
      array (
        'transport' => 'postmark',
      ),
      'resend' => 
      array (
        'transport' => 'resend',
      ),
      'sendmail' => 
      array (
        'transport' => 'sendmail',
        'path' => '/usr/sbin/sendmail -bs -i',
      ),
      'log' => 
      array (
        'transport' => 'log',
        'channel' => NULL,
      ),
      'array' => 
      array (
        'transport' => 'array',
      ),
      'failover' => 
      array (
        'transport' => 'failover',
        'mailers' => 
        array (
          0 => 'smtp',
          1 => 'log',
        ),
      ),
      'roundrobin' => 
      array (
        'transport' => 'roundrobin',
        'mailers' => 
        array (
          0 => 'ses',
          1 => 'postmark',
        ),
      ),
      'mailgun' => 
      array (
        'transport' => 'mailgun',
      ),
    ),
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Snipe-IT',
    ),
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/vendor/mail',
      ),
    ),
    'reply_to' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Snipe-IT',
    ),
  ),
  'mail-auto-embed' => 
  array (
    'enabled' => true,
    'method' => 'attachment',
    'curl' => 
    array (
      'connect_timeout' => 5,
      'timeout' => 10,
      'cache' => false,
      'cache_ttl' => 3600,
    ),
  ),
  'passport' => 
  array (
    'guard' => 'web',
    'private_key' => NULL,
    'public_key' => NULL,
    'connection' => NULL,
    'client_uuids' => false,
    'personal_access_client' => 
    array (
      'id' => NULL,
      'secret' => NULL,
    ),
    'expiration_years' => '15',
    'cookie_name' => 'snipeit_passport_token',
  ),
  'permissions' => 
  array (
    'Global' => 
    array (
      0 => 
      array (
        'permission' => 'superuser',
        'label' => 'Super User',
        'note' => 'Determines whether the user has full access to all aspects of the admin. This setting overrides any more specific permissions throughout the system. ',
        'display' => true,
      ),
    ),
    'Admin' => 
    array (
      0 => 
      array (
        'permission' => 'admin',
        'label' => '',
        'note' => 'Determines whether the user has access to most aspects of the admin. ',
        'display' => true,
      ),
    ),
    'CSV Import' => 
    array (
      0 => 
      array (
        'permission' => 'import',
        'label' => '',
        'note' => 'This will allow users to import even if access to users, assets, etc is denied elsewhere.',
        'display' => true,
      ),
    ),
    'Reports' => 
    array (
      0 => 
      array (
        'permission' => 'reports.view',
        'label' => 'View',
        'note' => 'Determines whether the user has the ability to view reports.',
        'display' => true,
      ),
    ),
    'Assets' => 
    array (
      0 => 
      array (
        'permission' => 'assets.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'assets.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'assets.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'assets.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
      4 => 
      array (
        'permission' => 'assets.checkout',
        'label' => 'Checkout ',
        'note' => '',
        'display' => false,
      ),
      5 => 
      array (
        'permission' => 'assets.checkin',
        'label' => 'Checkin ',
        'note' => '',
        'display' => true,
      ),
      6 => 
      array (
        'permission' => 'assets.checkout',
        'label' => 'Checkout ',
        'note' => '',
        'display' => true,
      ),
      7 => 
      array (
        'permission' => 'assets.audit',
        'label' => 'Audit ',
        'note' => 'Allows the user to mark an asset as physically inventoried.',
        'display' => true,
      ),
      8 => 
      array (
        'permission' => 'assets.view.requestable',
        'label' => 'View Requestable Assets',
        'note' => '',
        'display' => true,
      ),
      9 => 
      array (
        'permission' => 'assets.view.encrypted_custom_fields',
        'label' => 'View and Modify Encrypted Custom Fields',
        'note' => '',
        'display' => true,
      ),
    ),
    'Accessories' => 
    array (
      0 => 
      array (
        'permission' => 'accessories.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'accessories.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'accessories.edit',
        'label' => 'Edit ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'accessories.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
      4 => 
      array (
        'permission' => 'accessories.checkout',
        'label' => 'Checkout ',
        'note' => '',
        'display' => true,
      ),
      5 => 
      array (
        'permission' => 'accessories.checkin',
        'label' => 'Checkin ',
        'note' => '',
        'display' => true,
      ),
      6 => 
      array (
        'permission' => 'accessories.files',
        'label' => 'View and Modify Accessory Files',
        'note' => '',
        'display' => true,
      ),
    ),
    'Consumables' => 
    array (
      0 => 
      array (
        'permission' => 'consumables.view',
        'label' => 'View',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'consumables.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'consumables.edit',
        'label' => 'Edit ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'consumables.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
      4 => 
      array (
        'permission' => 'consumables.checkout',
        'label' => 'Checkout ',
        'note' => '',
        'display' => true,
      ),
      5 => 
      array (
        'permission' => 'consumables.files',
        'label' => 'View and Modify Consumable Files',
        'note' => '',
        'display' => true,
      ),
    ),
    'Licenses' => 
    array (
      0 => 
      array (
        'permission' => 'licenses.view',
        'label' => 'View',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'licenses.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'licenses.edit',
        'label' => 'Edit ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'licenses.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
      4 => 
      array (
        'permission' => 'licenses.checkout',
        'label' => 'Checkout ',
        'note' => '',
        'display' => true,
      ),
      5 => 
      array (
        'permission' => 'licenses.keys',
        'label' => 'View License Keys',
        'note' => '',
        'display' => true,
      ),
      6 => 
      array (
        'permission' => 'licenses.files',
        'label' => 'View and Modify License Files',
        'note' => '',
        'display' => true,
      ),
    ),
    'Components' => 
    array (
      0 => 
      array (
        'permission' => 'components.view',
        'label' => 'View',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'components.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'components.edit',
        'label' => 'Edit ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'components.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
      4 => 
      array (
        'permission' => 'components.checkout',
        'label' => 'Checkout ',
        'note' => '',
        'display' => true,
      ),
      5 => 
      array (
        'permission' => 'components.checkin',
        'label' => 'Checkin ',
        'note' => '',
        'display' => true,
      ),
      6 => 
      array (
        'permission' => 'components.files',
        'label' => 'View and Modify Component Files',
        'note' => '',
        'display' => true,
      ),
    ),
    'Kits' => 
    array (
      0 => 
      array (
        'permission' => 'kits.view',
        'label' => 'View ',
        'note' => 'These are predefined kits that can be used to quickly checkout assets, licenses, etc.',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'kits.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'kits.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'kits.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Users' => 
    array (
      0 => 
      array (
        'permission' => 'users.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'users.create',
        'label' => 'Create Users',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'users.edit',
        'label' => 'Edit Users',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'users.delete',
        'label' => 'Delete Users',
        'note' => '',
        'display' => true,
      ),
    ),
    'Models' => 
    array (
      0 => 
      array (
        'permission' => 'models.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'models.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'models.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'models.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Categories' => 
    array (
      0 => 
      array (
        'permission' => 'categories.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'categories.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'categories.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'categories.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Departments' => 
    array (
      0 => 
      array (
        'permission' => 'departments.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'departments.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'departments.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'departments.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Status Labels' => 
    array (
      0 => 
      array (
        'permission' => 'statuslabels.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'statuslabels.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'statuslabels.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'statuslabels.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Custom Fields' => 
    array (
      0 => 
      array (
        'permission' => 'customfields.view',
        'label' => 'View',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'customfields.create',
        'label' => 'Create',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'customfields.edit',
        'label' => 'Edit',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'customfields.delete',
        'label' => 'Delete',
        'note' => '',
        'display' => true,
      ),
    ),
    'Suppliers' => 
    array (
      0 => 
      array (
        'permission' => 'suppliers.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'suppliers.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'suppliers.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'suppliers.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Manufacturers' => 
    array (
      0 => 
      array (
        'permission' => 'manufacturers.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'manufacturers.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'manufacturers.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'manufacturers.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Depreciations' => 
    array (
      0 => 
      array (
        'permission' => 'depreciations.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'depreciations.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'depreciations.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'depreciations.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Locations' => 
    array (
      0 => 
      array (
        'permission' => 'locations.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'locations.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'locations.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'locations.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Companies' => 
    array (
      0 => 
      array (
        'permission' => 'companies.view',
        'label' => 'View ',
        'note' => '',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'companies.create',
        'label' => 'Create ',
        'note' => '',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'companies.edit',
        'label' => 'Edit  ',
        'note' => '',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'companies.delete',
        'label' => 'Delete ',
        'note' => '',
        'display' => true,
      ),
    ),
    'Self' => 
    array (
      0 => 
      array (
        'permission' => 'self.two_factor',
        'label' => 'Two-Factor Authentication',
        'note' => 'The user may disable/enable two-factor authentication themselves if two-factor is enabled and set to selective.',
        'display' => true,
      ),
      1 => 
      array (
        'permission' => 'self.api',
        'label' => 'Create API Keys',
        'note' => 'The user create personal API keys to utilize the REST API.',
        'display' => true,
      ),
      2 => 
      array (
        'permission' => 'self.edit_location',
        'label' => 'Profile Edit Location',
        'note' => 'The user may update their own location in their profile. Note that this is not affected by any additional Users permissions you grant to this user or group.',
        'display' => true,
      ),
      3 => 
      array (
        'permission' => 'self.checkout_assets',
        'label' => 'Self-Checkout',
        'note' => 'This user may check out assets that are marked for self-checkout.',
        'display' => true,
      ),
      4 => 
      array (
        'permission' => 'self.view_purchase_cost',
        'label' => 'View Purchase-Cost Column',
        'note' => 'This user can see the purchase cost column of items assigned to them.',
        'display' => true,
      ),
    ),
  ),
  'queue' => 
  array (
    'default' => 'sync',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => false,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
        'after_commit' => false,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => NULL,
        'secret' => NULL,
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'default',
        'suffix' => NULL,
        'region' => NULL,
        'after_commit' => false,
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
        'after_commit' => false,
      ),
    ),
    'batching' => 
    array (
      'database' => 'mysql',
      'table' => 'job_batches',
    ),
    'failed' => 
    array (
      'driver' => 'database-uuids',
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'scim' => 
  array (
    'publish_routes' => false,
    'omit_main_schema_in_return' => false,
    'trace' => false,
  ),
  'services' => 
  array (
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'ses' => 
    array (
      'key' => NULL,
      'secret' => NULL,
      'region' => NULL,
    ),
    'resend' => 
    array (
      'key' => NULL,
    ),
    'slack' => 
    array (
      'notifications' => 
      array (
        'bot_user_oauth_token' => NULL,
        'channel' => NULL,
      ),
    ),
    'mailgun' => 
    array (
      'domain' => NULL,
      'secret' => NULL,
      'endpoint' => 'api.mailgun.net',
    ),
    'mandrill' => 
    array (
      'secret' => NULL,
    ),
    'stripe' => 
    array (
      'model' => 'App\\User',
      'key' => NULL,
      'secret' => NULL,
    ),
    'google' => 
    array (
      'maps_api_key' => '',
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => '12000',
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'snipeit_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => false,
    'http_only' => true,
    'same_site' => 'lax',
    'partitioned' => false,
    'bs_table_storage' => 'localStorage',
    'bs_table_addrbar' => true,
  ),
  'trustedproxy' => 
  array (
    'proxies' => 
    array (
      0 => '***********',
      1 => '********',
    ),
  ),
  'version' => 
  array (
    'app_version' => 'v8.2.1',
    'full_app_version' => 'v8.2.1 - build 19068-g6ca49a20c',
    'build_version' => '19068',
    'prerelease_version' => '',
    'hash_version' => 'g6ca49a20c',
    'full_hash' => 'v8.2.1-10-g6ca49a20c',
    'branch' => 'master',
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views',
    ),
    'compiled' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\storage\\framework\\views',
  ),
  'concurrency' => 
  array (
    'default' => 'process',
  ),
  'google-chat' => 
  array (
    'space' => NULL,
    'spaces' => 
    array (
    ),
  ),
  'teams' => 
  array (
    'webhook_url' => NULL,
    'verify_ssl' => true,
  ),
  'flare' => 
  array (
    'key' => NULL,
    'flare_middleware' => 
    array (
      0 => 'Spatie\\FlareClient\\FlareMiddleware\\RemoveRequestIp',
      1 => 'Spatie\\FlareClient\\FlareMiddleware\\AddGitInformation',
      2 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddNotifierName',
      3 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddEnvironmentInformation',
      4 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionInformation',
      5 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddDumps',
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddLogs' => 
      array (
        'maximum_number_of_collected_logs' => 200,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddQueries' => 
      array (
        'maximum_number_of_collected_queries' => 200,
        'report_query_bindings' => true,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddJobs' => 
      array (
        'max_chained_job_reporting_depth' => 5,
      ),
      6 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddContext',
      7 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionHandledStatus',
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestBodyFields' => 
      array (
        'censor_fields' => 
        array (
          0 => 'password',
          1 => 'password_confirmation',
        ),
      ),
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestHeaders' => 
      array (
        'headers' => 
        array (
          0 => 'API-KEY',
          1 => 'Authorization',
          2 => 'Cookie',
          3 => 'Set-Cookie',
          4 => 'X-CSRF-TOKEN',
          5 => 'X-XSRF-TOKEN',
        ),
      ),
    ),
    'send_logs_as_events' => true,
  ),
  'ignition' => 
  array (
    'editor' => 'phpstorm',
    'theme' => 'auto',
    'enable_share_button' => true,
    'register_commands' => false,
    'solution_providers' => 
    array (
      0 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\BadMethodCallSolutionProvider',
      1 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider',
      2 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\UndefinedPropertySolutionProvider',
      3 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\IncorrectValetDbCredentialsSolutionProvider',
      4 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingAppKeySolutionProvider',
      5 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\DefaultDbNameSolutionProvider',
      6 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\TableNotFoundSolutionProvider',
      7 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingImportSolutionProvider',
      8 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\InvalidRouteActionSolutionProvider',
      9 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\ViewNotFoundSolutionProvider',
      10 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\RunningLaravelDuskInProductionProvider',
      11 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingColumnSolutionProvider',
      12 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownValidationSolutionProvider',
      13 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingMixManifestSolutionProvider',
      14 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingViteManifestSolutionProvider',
      15 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingLivewireComponentSolutionProvider',
      16 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UndefinedViewVariableSolutionProvider',
      17 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\GenericLaravelExceptionSolutionProvider',
      18 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\OpenAiSolutionProvider',
      19 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\SailNetworkSolutionProvider',
      20 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMysql8CollationSolutionProvider',
      21 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownMariadbCollationSolutionProvider',
    ),
    'ignored_solution_providers' => 
    array (
    ),
    'enable_runnable_solutions' => NULL,
    'remote_sites_path' => 'C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master',
    'local_sites_path' => '',
    'housekeeping_endpoint_prefix' => '_ignition',
    'settings_file_path' => '',
    'recorders' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder',
      1 => 'Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder',
      2 => 'Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder',
      3 => 'Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder',
    ),
    'open_ai_key' => NULL,
    'with_stack_frame_arguments' => true,
    'argument_reducers' => 
    array (
      0 => 'Spatie\\Backtrace\\Arguments\\Reducers\\BaseTypeArgumentReducer',
      1 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ArrayArgumentReducer',
      2 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StdClassArgumentReducer',
      3 => 'Spatie\\Backtrace\\Arguments\\Reducers\\EnumArgumentReducer',
      4 => 'Spatie\\Backtrace\\Arguments\\Reducers\\ClosureArgumentReducer',
      5 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeArgumentReducer',
      6 => 'Spatie\\Backtrace\\Arguments\\Reducers\\DateTimeZoneArgumentReducer',
      7 => 'Spatie\\Backtrace\\Arguments\\Reducers\\SymphonyRequestArgumentReducer',
      8 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\ModelArgumentReducer',
      9 => 'Spatie\\LaravelIgnition\\ArgumentReducers\\CollectionArgumentReducer',
      10 => 'Spatie\\Backtrace\\Arguments\\Reducers\\StringableArgumentReducer',
    ),
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
