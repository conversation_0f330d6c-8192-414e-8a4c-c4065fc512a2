{"__meta": {"id": "01K2S04MENQKFTVP314ZGKNA41", "datetime": "2025-08-16 08:32:33", "utime": **********.240965, "method": "GET", "uri": "/setup", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 4, "messages": [{"message": "[08:32:23] LOG.debug: ErrorException: Attempt to read property \"show_archived_in_list\" on null in C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php:38\nStack trace:\n#0 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 38)\n#1 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php(38): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 38)\n#2 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\AssetCountForSidebar->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#3 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#4 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#5 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#6 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckForTwoFactor->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#7 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckUserIsActivated.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#8 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckUserIsActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#9 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckLocale.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#10 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#11 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#12 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#13 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#14 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#15 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#16 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#17 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#18 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))\n#19 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))\n#20 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))\n#21 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))\n#22 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))\n#23 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))\n#24 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#25 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#26 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#27 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#28 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#29 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#30 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\PreventBackHistory.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#31 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#32 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\SecurityHeaders.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#33 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#34 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#35 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#36 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#37 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#38 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#39 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#40 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForDebug.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#41 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckForDebug->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#42 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForSetup.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#43 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckForSetup->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#44 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#45 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#46 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#47 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))\n#48 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#49 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#50 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#51 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\NoSessionStore.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#52 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\NoSessionStore->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#53 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#54 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#55 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#56 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))\n#57 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))\n#58 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))\n#59 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')\n#60 {main}", "message_html": null, "is_string": false, "label": "debug", "time": **********.08821, "xdebug_link": null, "collector": "log"}, {"message": "[08:32:23] LOG.debug: ErrorException: Attempt to read property \"audit_warning_days\" on null in C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Asset.php:1358\nStack trace:\n#0 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 1358)\n#1 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Asset.php(1358): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 1358)\n#2 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1654): App\\Models\\Asset->scopeDueForAudit(Object(Illuminate\\Database\\Eloquent\\Builder), NULL)\n#3 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1529): Illuminate\\Database\\Eloquent\\Model->callNamedScope('DueForAudit', Array)\n#4 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1510): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Illuminate\\Database\\Eloquent\\Builder), NULL)\n#5 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1528): Illuminate\\Database\\Eloquent\\Builder->callScope(Object(Closure), Array)\n#6 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2116): Illuminate\\Database\\Eloquent\\Builder->callNamedScope('DueForAudit', Array)\n#7 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('DueForAudit', Array)\n#8 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'DueForAudit', Array)\n#9 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->__call('DueForAudit', Array)\n#10 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php(89): Illuminate\\Database\\Eloquent\\Model::__callStatic('DueForAudit', Array)\n#11 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\AssetCountForSidebar->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#12 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#13 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#14 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#15 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckForTwoFactor->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#16 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckUserIsActivated.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#17 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckUserIsActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#18 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckLocale.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#19 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#20 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#21 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#22 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#23 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#24 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#25 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#26 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#27 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))\n#28 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))\n#29 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))\n#30 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))\n#31 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))\n#32 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))\n#33 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#34 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#35 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#36 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#37 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#38 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#39 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\PreventBackHistory.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#40 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#41 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\SecurityHeaders.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#42 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#43 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#44 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#45 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#46 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#47 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#48 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#49 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForDebug.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#50 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckForDebug->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#51 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForSetup.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#52 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckForSetup->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#53 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#54 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#55 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#56 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))\n#57 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#58 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#59 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#60 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\NoSessionStore.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#61 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\NoSessionStore->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#62 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#63 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#64 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#65 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))\n#66 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))\n#67 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))\n#68 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')\n#69 {main}", "message_html": null, "is_string": false, "label": "debug", "time": **********.125268, "xdebug_link": null, "collector": "log"}, {"message": "[08:32:23] LOG.debug: ErrorException: Attempt to read property \"due_checkin_days\" on null in C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Asset.php:1430\nStack trace:\n#0 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 1430)\n#1 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Asset.php(1430): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 1430)\n#2 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1654): App\\Models\\Asset->scopeDueForCheckin(Object(Illuminate\\Database\\Eloquent\\Builder), NULL)\n#3 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1529): Illuminate\\Database\\Eloquent\\Model->callNamedScope('DueForCheckin', Array)\n#4 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1510): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Illuminate\\Database\\Eloquent\\Builder), NULL)\n#5 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1528): Illuminate\\Database\\Eloquent\\Builder->callScope(Object(Closure), Array)\n#6 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2116): Illuminate\\Database\\Eloquent\\Builder->callNamedScope('DueForCheckin', Array)\n#7 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('DueForCheckin', Array)\n#8 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2372): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'DueForCheckin', Array)\n#9 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->__call('DueForCheckin', Array)\n#10 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php(103): Illuminate\\Database\\Eloquent\\Model::__callStatic('DueForCheckin', Array)\n#11 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\AssetCountForSidebar->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#12 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#13 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#14 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#15 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckForTwoFactor->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#16 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckUserIsActivated.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#17 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckUserIsActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#18 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckLocale.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#19 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#20 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#21 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#22 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#23 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#24 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#25 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#26 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#27 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))\n#28 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))\n#29 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))\n#30 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))\n#31 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))\n#32 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))\n#33 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#34 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#35 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#36 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#37 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#38 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#39 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\PreventBackHistory.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#40 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#41 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\SecurityHeaders.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#42 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#43 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#44 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#45 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#46 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#47 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#48 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#49 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForDebug.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#50 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckForDebug->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#51 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForSetup.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#52 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckForSetup->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#53 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#54 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#55 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#56 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))\n#57 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#58 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#59 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#60 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\NoSessionStore.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#61 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\NoSessionStore->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#62 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#63 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))\n#64 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))\n#65 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))\n#66 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))\n#67 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))\n#68 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')\n#69 {main}", "message_html": null, "is_string": false, "label": "debug", "time": **********.130211, "xdebug_link": null, "collector": "log"}, {"message": "[08:32:33] LOG.debug: cURL error 28: Operation timed out after 10005 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:8000/.env", "message_html": null, "is_string": false, "label": "debug", "time": **********.177914, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 35, "start": **********.563201, "end": **********.241082, "duration": 10.677881002426147, "duration_str": "10.68s", "measures": [{"label": "Booting", "start": **********.563201, "relative_start": 0, "end": **********.912269, "relative_end": **********.912269, "duration": 0.*****************, "duration_str": "349ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.912303, "relative_start": 0.****************, "end": **********.241091, "relative_end": 9.059906005859375e-06, "duration": 10.***************, "duration_str": "10.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.031022, "relative_start": 0.****************, "end": **********.044626, "relative_end": **********.044626, "duration": 0.013603925704956055, "duration_str": "13.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select * from `settings` limit 1", "start": **********.047547, "relative_start": 0.****************, "end": **********.049437, "relative_end": **********.049437, "duration": 0.001889944076538086, "duration_str": "1.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `users` where `id` = ? and `users`.`deleted_at` is null limit 1", "start": **********.0520902, "relative_start": 0.***************, "end": **********.05273, "relative_end": **********.05273, "duration": 0.0006399154663085938, "duration_str": "640μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.055033, "relative_start": 0.49183201789855957, "end": **********.055873, "relative_end": **********.055873, "duration": 0.0008399486541748047, "duration_str": "840μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `users` where `id` = ? and `users`.`deleted_at` is null limit 1", "start": **********.057717, "relative_start": 0.49451613426208496, "end": **********.058227, "relative_end": **********.058227, "duration": 0.0005099773406982422, "duration_str": "510μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.070014, "relative_start": 0.5068130493164062, "end": **********.071354, "relative_end": **********.071354, "duration": 0.0013399124145507812, "duration_str": "1.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.074579, "relative_start": 0.5113780498504639, "end": **********.075729, "relative_end": **********.075729, "duration": 0.001149892807006836, "duration_str": "1.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `users` where `id` = ? and `users`.`deleted_at` is null limit 1", "start": **********.077314, "relative_start": 0.5141129493713379, "end": **********.078204, "relative_end": **********.078204, "duration": 0.0008900165557861328, "duration_str": "890μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.080559, "relative_start": 0.5173580646514893, "end": **********.081629, "relative_end": **********.081629, "duration": 0.0010700225830078125, "duration_str": "1.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.0837011, "relative_start": 0.5205001831054688, "end": **********.084531, "relative_end": **********.084531, "duration": 0.0008299350738525391, "duration_str": "830μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "start": **********.085895, "relative_start": 0.5226941108703613, "end": **********.086615, "relative_end": **********.086615, "duration": 0.0007200241088867188, "duration_str": "720μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.091403, "relative_start": 0.5282020568847656, "end": **********.092323, "relative_end": **********.092323, "duration": 0.0009200572967529297, "duration_str": "920μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select *...", "start": **********.093868, "relative_start": 0.5306670665740967, "end": **********.094508, "relative_end": **********.094508, "duration": 0.0006399154663085938, "duration_str": "640μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.096301, "relative_start": 0.5331001281738281, "end": **********.097301, "relative_end": **********.097301, "duration": 0.0009999275207519531, "duration_str": "1000μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assigned_to` > ? and `assets`.`deleted_at` is null", "start": **********.0996869, "relative_start": 0.5364859104156494, "end": **********.100607, "relative_end": **********.100607, "duration": 0.0009200572967529297, "duration_str": "920μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.102911, "relative_start": 0.5397100448608398, "end": **********.104021, "relative_end": **********.104021, "duration": 0.001110076904296875, "duration_str": "1.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.1058109, "relative_start": 0.5426099300384521, "end": **********.106601, "relative_end": **********.106601, "duration": 0.0007901191711425781, "duration_str": "790μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.108648, "relative_start": 0.5454471111297607, "end": **********.109328, "relative_end": **********.109328, "duration": 0.0006799697875976562, "duration_str": "680μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.1107, "relative_start": 0.5474989414215088, "end": **********.11142, "relative_end": **********.11142, "duration": 0.0007200241088867188, "duration_str": "720μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.113582, "relative_start": 0.5503809452056885, "end": **********.114852, "relative_end": **********.114852, "duration": 0.0012700557708740234, "duration_str": "1.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets...", "start": **********.116544, "relative_start": 0.5533430576324463, "end": **********.117304, "relative_end": **********.117304, "duration": 0.0007600784301757812, "duration_str": "760μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.120631, "relative_start": 0.5574300289154053, "end": **********.121641, "relative_end": **********.121641, "duration": 0.0010099411010742188, "duration_str": "1.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `byod` = ? and `assets`.`deleted_at` is null", "start": **********.123095, "relative_start": 0.5598940849304199, "end": **********.123555, "relative_end": **********.123555, "duration": 0.00045990943908691406, "duration_str": "460μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.125804, "relative_start": 0.5626029968261719, "end": **********.126534, "relative_end": **********.126534, "duration": 0.0007300376892089844, "duration_str": "730μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`...", "start": **********.127954, "relative_start": 0.5647530555725098, "end": **********.128494, "relative_end": **********.128494, "duration": 0.0005400180816650391, "duration_str": "540μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.130964, "relative_start": 0.567763090133667, "end": **********.131664, "relative_end": **********.131664, "duration": 0.0006999969482421875, "duration_str": "700μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets...", "start": **********.133358, "relative_start": 0.5701570510864258, "end": **********.133828, "relative_end": **********.133828, "duration": 0.0004699230194091797, "duration_str": "470μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.135952, "relative_start": 0.5727510452270508, "end": **********.136672, "relative_end": **********.136672, "duration": 0.0007200241088867188, "duration_str": "720μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `users` where `id` = ? and `users`.`deleted_at` is null limit 1", "start": **********.138292, "relative_start": 0.5750911235809326, "end": **********.138952, "relative_end": **********.138952, "duration": 0.000659942626953125, "duration_str": "660μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select 2 + 2", "start": **********.142179, "relative_start": 0.5789780616760254, "end": **********.142489, "relative_end": **********.142489, "duration": 0.00030994415283203125, "duration_str": "310μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "Preparing Response", "start": **********.188188, "relative_start": 10.624987125396729, "end": **********.230759, "relative_end": **********.230759, "duration": 0.04257082939147949, "duration_str": "42.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "select * from `settings` limit 1", "start": **********.1947951, "relative_start": 10.631594181060791, "end": **********.197405, "relative_end": **********.197405, "duration": 0.0026099681854248047, "duration_str": "2.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}, {"label": "select * from `settings` limit 1", "start": **********.213675, "relative_start": 10.650474071502686, "end": **********.216405, "relative_end": **********.216405, "duration": 0.0027298927307128906, "duration_str": "2.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "db", "group": "Database Query"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.2.12", "Environment": "production", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "UTC", "Locale": "en-US"}}, "event": {"count": 79, "start": **********.563201, "end": **********.241464, "duration": 10.***************, "duration_str": "10.68s", "measures": [{"label": "Illuminate\\Routing\\Events\\Routing", "start": **********.03103, "relative_start": 0.*****************, "end": **********.03103, "relative_end": **********.03103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\Routing"}, {"label": "Illuminate\\Routing\\Events\\RouteMatched", "start": **********.044647, "relative_start": 0.****************, "end": **********.044647, "relative_end": **********.044647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\RouteMatched"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.048198, "relative_start": 0.***************, "end": **********.048198, "relative_end": **********.048198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.05183, "relative_start": 0.****************, "end": **********.05183, "relative_end": **********.05183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.052388, "relative_start": 0.*****************, "end": **********.052388, "relative_end": **********.052388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.054668, "relative_start": 0.4914669990539551, "end": **********.054668, "relative_end": **********.054668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.055302, "relative_start": 0.49210095405578613, "end": **********.055302, "relative_end": **********.055302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.057429, "relative_start": 0.4942281246185303, "end": **********.057429, "relative_end": **********.057429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.057918, "relative_start": 0.4947171211242676, "end": **********.057918, "relative_end": **********.057918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.059719, "relative_start": 0.4965181350708008, "end": **********.059719, "relative_end": **********.059719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.070682, "relative_start": 0.5074810981750488, "end": **********.070682, "relative_end": **********.070682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.073503, "relative_start": 0.5103020668029785, "end": **********.073503, "relative_end": **********.073503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Foundation\\Events\\LocaleUpdated", "start": **********.073895, "relative_start": 0.5106940269470215, "end": **********.073895, "relative_end": **********.073895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Foundation\\Events\\LocaleUpdated"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.075021, "relative_start": 0.5118200778961182, "end": **********.075021, "relative_end": **********.075021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.077046, "relative_start": 0.5138449668884277, "end": **********.077046, "relative_end": **********.077046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.077724, "relative_start": 0.5145230293273926, "end": **********.077724, "relative_end": **********.077724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.079667, "relative_start": 0.5164661407470703, "end": **********.079667, "relative_end": **********.079667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.081018, "relative_start": 0.5178170204162598, "end": **********.081018, "relative_end": **********.081018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.083376, "relative_start": 0.5201749801635742, "end": **********.083376, "relative_end": **********.083376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.084107, "relative_start": 0.5209059715270996, "end": **********.084107, "relative_end": **********.084107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.08565, "relative_start": 0.5224490165710449, "end": **********.08565, "relative_end": **********.08565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.086181, "relative_start": 0.5229799747467041, "end": **********.086181, "relative_end": **********.086181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.087933, "relative_start": 0.5247321128845215, "end": **********.087933, "relative_end": **********.087933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Log\\Events\\MessageLogged", "start": **********.088377, "relative_start": 0.5251760482788086, "end": **********.088377, "relative_end": **********.088377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Log\\Events\\MessageLogged"}, {"label": "eloquent.booting: App\\Models\\Statuslabel", "start": **********.089792, "relative_start": 0.5265910625457764, "end": **********.089792, "relative_end": **********.089792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booting"}, {"label": "eloquent.booted: App\\Models\\Statuslabel", "start": **********.089931, "relative_start": 0.5267300605773926, "end": **********.089931, "relative_end": **********.089931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "eloquent.booted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.091862, "relative_start": 0.5286610126495361, "end": **********.091862, "relative_end": **********.091862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.093577, "relative_start": 0.5303759574890137, "end": **********.093577, "relative_end": **********.093577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.094112, "relative_start": 0.5309109687805176, "end": **********.094112, "relative_end": **********.094112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.095982, "relative_start": 0.5327811241149902, "end": **********.095982, "relative_end": **********.095982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.096586, "relative_start": 0.5333850383758545, "end": **********.096586, "relative_end": **********.096586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.099337, "relative_start": 0.5361361503601074, "end": **********.099337, "relative_end": **********.099337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.100074, "relative_start": 0.5368731021881104, "end": **********.100074, "relative_end": **********.100074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.102408, "relative_start": 0.5392069816589355, "end": **********.102408, "relative_end": **********.102408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.103403, "relative_start": 0.5402021408081055, "end": **********.103403, "relative_end": **********.103403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.105502, "relative_start": 0.5423009395599365, "end": **********.105502, "relative_end": **********.105502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.106058, "relative_start": 0.5428569316864014, "end": **********.106058, "relative_end": **********.106058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.108216, "relative_start": 0.5450150966644287, "end": **********.108216, "relative_end": **********.108216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.108937, "relative_start": 0.5457360744476318, "end": **********.108937, "relative_end": **********.108937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.110465, "relative_start": 0.5472640991210938, "end": **********.110465, "relative_end": **********.110465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.111024, "relative_start": 0.5478229522705078, "end": **********.111024, "relative_end": **********.111024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.113148, "relative_start": 0.5499470233917236, "end": **********.113148, "relative_end": **********.113148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.114137, "relative_start": 0.5509359836578369, "end": **********.114137, "relative_end": **********.114137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.116212, "relative_start": 0.5530109405517578, "end": **********.116212, "relative_end": **********.116212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.116748, "relative_start": 0.5535471439361572, "end": **********.116748, "relative_end": **********.116748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.120316, "relative_start": 0.5571150779724121, "end": **********.120316, "relative_end": **********.120316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.12107, "relative_start": 0.5578689575195312, "end": **********.12107, "relative_end": **********.12107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.122871, "relative_start": 0.5596699714660645, "end": **********.122871, "relative_end": **********.122871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.123282, "relative_start": 0.5600810050964355, "end": **********.123282, "relative_end": **********.123282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.124989, "relative_start": 0.5617880821228027, "end": **********.124989, "relative_end": **********.124989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Log\\Events\\MessageLogged", "start": **********.125336, "relative_start": 0.5621349811553955, "end": **********.125336, "relative_end": **********.125336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Log\\Events\\MessageLogged"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.126121, "relative_start": 0.562920093536377, "end": **********.126121, "relative_end": **********.126121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.127694, "relative_start": 0.56449294090271, "end": **********.127694, "relative_end": **********.127694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.128142, "relative_start": 0.5649411678314209, "end": **********.128142, "relative_end": **********.128142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.129915, "relative_start": 0.5667140483856201, "end": **********.129915, "relative_end": **********.129915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Log\\Events\\MessageLogged", "start": **********.130283, "relative_start": 0.5670821666717529, "end": **********.130283, "relative_end": **********.130283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Log\\Events\\MessageLogged"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.13126, "relative_start": 0.568058967590332, "end": **********.13126, "relative_end": **********.13126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.133067, "relative_start": 0.5698659420013428, "end": **********.133067, "relative_end": **********.133067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.133512, "relative_start": 0.5703110694885254, "end": **********.133512, "relative_end": **********.133512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.135631, "relative_start": 0.5724301338195801, "end": **********.135631, "relative_end": **********.135631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.136244, "relative_start": 0.5730431079864502, "end": **********.136244, "relative_end": **********.136244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.138031, "relative_start": 0.5748300552368164, "end": **********.138031, "relative_end": **********.138031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.138602, "relative_start": 0.5754010677337646, "end": **********.138602, "relative_end": **********.138602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.140361, "relative_start": 0.577160120010376, "end": **********.140361, "relative_end": **********.140361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.142315, "relative_start": 0.5791139602661133, "end": **********.142315, "relative_end": **********.142315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.144825, "relative_start": 0.5816240310668945, "end": **********.144825, "relative_end": **********.144825, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "Illuminate\\Http\\Client\\Events\\RequestSending", "start": **********.162326, "relative_start": 0.5991251468658447, "end": **********.162326, "relative_end": **********.162326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Http\\Client\\Events\\RequestSending"}, {"label": "Illuminate\\Http\\Client\\Events\\ConnectionFailed", "start": **********.17758, "relative_start": 10.614379167556763, "end": **********.17758, "relative_end": **********.17758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Http\\Client\\Events\\ConnectionFailed"}, {"label": "Illuminate\\Log\\Events\\MessageLogged", "start": **********.178139, "relative_start": 10.614938020706177, "end": **********.178139, "relative_end": **********.178139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Log\\Events\\MessageLogged"}, {"label": "creating: setup.index", "start": **********.187421, "relative_start": 10.***************, "end": **********.187421, "relative_end": **********.187421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "Illuminate\\Routing\\Events\\PreparingResponse", "start": **********.188198, "relative_start": 10.**************, "end": **********.188198, "relative_end": **********.188198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\PreparingResponse"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.195828, "relative_start": 10.***************, "end": **********.195828, "relative_end": **********.195828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.207512, "relative_start": 10.**************, "end": **********.207512, "relative_end": **********.207512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "composing: setup.index", "start": **********.207869, "relative_start": 10.***************, "end": **********.207869, "relative_end": **********.207869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "creating: layouts.setup", "start": **********.213099, "relative_start": 10.649898052215576, "end": **********.213099, "relative_end": **********.213099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "creating"}, {"label": "Illuminate\\Database\\Events\\StatementPrepared", "start": **********.21463, "relative_start": 10.651428937911987, "end": **********.21463, "relative_end": **********.21463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\StatementPrepared"}, {"label": "Illuminate\\Database\\Events\\QueryExecuted", "start": **********.221538, "relative_start": 10.658337116241455, "end": **********.221538, "relative_end": **********.221538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Database\\Events\\QueryExecuted"}, {"label": "composing: layouts.setup", "start": **********.221783, "relative_start": 10.***************, "end": **********.221783, "relative_end": **********.221783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "composing"}, {"label": "Illuminate\\Routing\\Events\\ResponsePrepared", "start": **********.230782, "relative_start": 10.**************, "end": **********.230782, "relative_end": **********.230782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": "Illuminate\\Routing\\Events\\ResponsePrepared"}], "nb_measures": 79}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "1x setup.index", "param_count": null, "params": [], "start": **********.208438, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/setup/index.blade.phpsetup.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Fsetup%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "setup.index"}, {"name": "1x layouts.setup", "param_count": null, "params": [], "start": **********.222258, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\resources\\views/layouts/setup.blade.phplayouts.setup", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fresources%2Fviews%2Flayouts%2Fsetup.blade.php&line=1", "ajax": false, "filename": "setup.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.setup"}]}, "route": {"uri": "GET setup", "middleware": "web", "controller": "App\\Http\\Controllers\\SettingsController@getSetupIndex<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FSettingsController.php&line=60\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/setup", "as": "setup", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FSettingsController.php&line=60\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/SettingsController.php:60-126</a>"}, "queries": {"count": 31, "nb_statements": 31, "nb_visible_statements": 31, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029630000000000004, "accumulated_duration_str": "29.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}], "start": **********.047547, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Controller.php", "line": 37}], "start": **********.0520902, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}], "start": **********.055033, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\Controller.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.057717, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckLocale.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckLocale.php", "line": 33}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 88}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.070014, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}], "start": **********.074579, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/CheckUserIsActivated.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckUserIsActivated.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.077314, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 30}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 22, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.080559, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 31, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}], "start": **********.0837011, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 21, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 34}], "start": **********.085895, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:37", "source": {"index": 19, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=37", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "37"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}], "start": **********.091403, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`assigned_to` is null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 1 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 34}], "start": **********.093868, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:47", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=47", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "47"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}], "start": **********.096301, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assigned_to` > '0' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 34}], "start": **********.0996869, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:54", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=54", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "54"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}], "start": **********.102911, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 1 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 1], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 34}], "start": **********.1058109, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:61", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=61", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "61"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}], "start": **********.108648, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 1 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 1, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 34}], "start": **********.1107, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:68", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=68", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "68"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}], "start": **********.113582, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `deployable` = 0 and `pending` = 0 and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": [0, 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 34}], "start": **********.116544, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:75", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=75", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "75"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}], "start": **********.120631, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `byod` = '1' and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 34}], "start": **********.123095, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:82", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=82", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "82"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}], "start": **********.125804, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`next_audit_date` is not null and `assets`.`next_audit_date` < '2025-08-16' and `assets`.`archived` = 0 and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 34}], "start": **********.127954, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:96", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=96", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "96"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}], "start": **********.130964, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select count(*) as aggregate from `assets` where `assets`.`expected_checkin` is not null and `assets`.`expected_checkin` < '2025-08-16' and `assets`.`archived` = 0 and `assets`.`assigned_to` is not null and exists (select * from `status_labels` where `assets`.`status_id` = `status_labels`.`id` and `archived` = 0 and `status_labels`.`deleted_at` is null) and `assets`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-08-16", 0, 0], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/passport/src/Http/Middleware/CreateFreshApiToken.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/CheckForTwoFactor.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\CheckForTwoFactor.php", "line": 34}], "start": **********.133358, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "AssetCountForSidebar.php:110", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/AssetCountForSidebar.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Middleware\\AssetCountForSidebar.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FMiddleware%2FAssetCountForSidebar.php&line=110", "ajax": false, "filename": "AssetCountForSidebar.php", "line": "110"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 78}, {"index": 21, "namespace": null, "name": "app/Models/Company.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Company.php", "line": 276}, {"index": 22, "namespace": null, "name": "app/Models/CompanyableScope.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\CompanyableScope.php", "line": 25}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}], "start": **********.135952, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.138292, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "snipeit", "explain": null}, {"sql": "select 2 + 2", "type": "query", "params": [], "bindings": [], "hints": ["The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/SettingsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\SettingsController.php", "line": 69}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.142179, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "SettingsController.php:69", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/SettingsController.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Http\\Controllers\\SettingsController.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FSettingsController.php&line=69", "ajax": false, "filename": "SettingsController.php", "line": "69"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Providers/SettingsServiceProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Providers\\SettingsServiceProvider.php", "line": 29}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}], "start": **********.1947951, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}, {"sql": "select * from `settings` limit 1", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, {"index": 20, "namespace": null, "name": "app/Providers/SettingsServiceProvider.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Providers\\SettingsServiceProvider.php", "line": 29}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 190}], "start": **********.213675, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "Setting.php:95", "source": {"index": 19, "namespace": null, "name": "app/Models/Setting.php", "file": "C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\app\\Models\\Setting.php", "line": 95}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FModels%2FSetting.php&line=95", "ajax": false, "filename": "Setting.php", "line": "95"}, "connection": "snipeit", "explain": null}]}, "models": {"data": [], "count": 0, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": []}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "logs": {"count": 3, "messages": [{"message": "[2025-08-16 08:27:31] local.ERROR: Maximum execution time of 600 seconds exceeded {\"exception\":\"[object] (Symfony\\\\Component\\\\ErrorHandler\\\\Error\\\\FatalError(code: 0): Maximum execution time of 600 seconds exceeded at C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Console\\\\ServeCommand.php:131)\n[stacktrace]\n#0 {main}\n\"} \n", "label": "error", "time": "2025-08-16 08:27:31", "collector": "laravel.log", "is_string": false}, {"message": "[2025-08-16 08:27:31] local.ERROR: Symfony\\Component\\ErrorHandler\\Error\\FatalError: Maximum execution time of 600 seconds exceeded in C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:131\nStack trace:\n#0 {main}  \n", "label": "error", "time": "2025-08-16 08:27:31", "collector": "laravel.log", "is_string": false}, {"message": "[2025-08-16 08:26:06] local.ERROR: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'model_warranty' (Connection: mysql, SQL: alter table `models` add `model_warranty` int null) {\"exception\":\"[object] (Illuminate\\\\Database\\\\QueryException(code: 42S21): SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'model_warranty' (Connection: mysql, SQL: alter table `models` add `model_warranty` int null) at C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php:825)\n[stacktrace]\n#0 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php(779): Illuminate\\\\Database\\\\Connection->runQueryCallback('alter table `mo...', Array, Object(Closure))\n#1 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php(560): Illuminate\\\\Database\\\\Connection->run('alter table `mo...', Array, Object(Closure))\n#2 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Schema\\\\Blueprint.php(118): Illuminate\\\\Database\\\\Connection->statement('alter table `mo...')\n#3 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Schema\\\\Builder.php(564): Illuminate\\\\Database\\\\Schema\\\\Blueprint->build(Object(Illuminate\\\\Database\\\\MySqlConnection), Object(Illuminate\\\\Database\\\\Schema\\\\Grammars\\\\MySqlGrammar))\n#4 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Schema\\\\Builder.php(406): Illuminate\\\\Database\\\\Schema\\\\Builder->build(Object(Illuminate\\\\Database\\\\Schema\\\\Blueprint))\n#5 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Support\\\\Facades\\\\Facade.php(361): Illuminate\\\\Database\\\\Schema\\\\Builder->table('models', Object(Closure))\n#6 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\database\\\\migrations\\\\2025_08_14_221801_add_model_warranty_to_models_table.php(14): Illuminate\\\\Support\\\\Facades\\\\Facade::__callStatic('table', Array)\n#7 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(507): Illuminate\\\\Database\\\\Migrations\\\\Migration@anonymous->up()\n#8 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(432): Illuminate\\\\Database\\\\Migrations\\\\Migrator->runMethod(Object(Illuminate\\\\Database\\\\MySqlConnection), Object(Illuminate\\\\Database\\\\Migrations\\\\Migration@anonymous), 'up')\n#9 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(441): Illuminate\\\\Database\\\\Migrations\\\\Migrator->Illuminate\\\\Database\\\\Migrations\\\\{closure}()\n#10 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(244): Illuminate\\\\Database\\\\Migrations\\\\Migrator->runMigration(Object(Illuminate\\\\Database\\\\Migrations\\\\Migration@anonymous), 'up')\n#11 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Console\\\\View\\\\Components\\\\Task.php(40): Illuminate\\\\Database\\\\Migrations\\\\Migrator->Illuminate\\\\Database\\\\Migrations\\\\{closure}()\n#12 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(798): Illuminate\\\\Console\\\\View\\\\Components\\\\Task->render('2025_08_14_2218...', Object(Closure))\n#13 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(244): Illuminate\\\\Database\\\\Migrations\\\\Migrator->write('Illuminate\\\\\\\\Cons...', '2025_08_14_2218...', Object(Closure))\n#14 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(211): Illuminate\\\\Database\\\\Migrations\\\\Migrator->runUp('C:\\\\\\\\xampp\\\\\\\\htdocs...', 1, false)\n#15 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(138): Illuminate\\\\Database\\\\Migrations\\\\Migrator->runPending(Array, Array)\n#16 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand.php(117): Illuminate\\\\Database\\\\Migrations\\\\Migrator->run(Array, Array)\n#17 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(658): Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand->Illuminate\\\\Database\\\\Console\\\\Migrations\\\\{closure}()\n#18 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand.php(110): Illuminate\\\\Database\\\\Migrations\\\\Migrator->usingConnection(NULL, Object(Closure))\n#19 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand.php(89): Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand->runMigrations()\n#20 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(36): Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand->handle()\n#21 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\Util.php(43): Illuminate\\\\Container\\\\BoundMethod::Illuminate\\\\Container\\\\{closure}()\n#22 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(95): Illuminate\\\\Container\\\\Util::unwrapIfClosure(Object(Closure))\n#23 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(35): Illuminate\\\\Container\\\\BoundMethod::callBoundMethod(Object(Illuminate\\\\Foundation\\\\Application), Array, Object(Closure))\n#24 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\Container.php(696): Illuminate\\\\Container\\\\BoundMethod::call(Object(Illuminate\\\\Foundation\\\\Application), Array, Array, NULL)\n#25 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Console\\\\Command.php(213): Illuminate\\\\Container\\\\Container->call(Array)\n#26 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\symfony\\\\console\\\\Command\\\\Command.php(318): Illuminate\\\\Console\\\\Command->execute(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Illuminate\\\\Console\\\\OutputStyle))\n#27 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Console\\\\Command.php(182): Symfony\\\\Component\\\\Console\\\\Command\\\\Command->run(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Illuminate\\\\Console\\\\OutputStyle))\n#28 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\symfony\\\\console\\\\Application.php(1092): Illuminate\\\\Console\\\\Command->run(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#29 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\symfony\\\\console\\\\Application.php(341): Symfony\\\\Component\\\\Console\\\\Application->doRunCommand(Object(Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand), Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#30 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\symfony\\\\console\\\\Application.php(192): Symfony\\\\Component\\\\Console\\\\Application->doRun(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#31 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Console\\\\Kernel.php(198): Symfony\\\\Component\\\\Console\\\\Application->run(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#32 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\artisan(33): Illuminate\\\\Foundation\\\\Console\\\\Kernel->handle(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#33 {main}\n\n[previous exception] [object] (PDOException(code: 42S21): SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'model_warranty' at C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php:571)\n[stacktrace]\n#0 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php(571): PDOStatement->execute()\n#1 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php(812): Illuminate\\\\Database\\\\Connection->Illuminate\\\\Database\\\\{closure}('alter table `mo...', Array)\n#2 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php(779): Illuminate\\\\Database\\\\Connection->runQueryCallback('alter table `mo...', Array, Object(Closure))\n#3 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Connection.php(560): Illuminate\\\\Database\\\\Connection->run('alter table `mo...', Array, Object(Closure))\n#4 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Schema\\\\Blueprint.php(118): Illuminate\\\\Database\\\\Connection->statement('alter table `mo...')\n#5 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Schema\\\\Builder.php(564): Illuminate\\\\Database\\\\Schema\\\\Blueprint->build(Object(Illuminate\\\\Database\\\\MySqlConnection), Object(Illuminate\\\\Database\\\\Schema\\\\Grammars\\\\MySqlGrammar))\n#6 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Schema\\\\Builder.php(406): Illuminate\\\\Database\\\\Schema\\\\Builder->build(Object(Illuminate\\\\Database\\\\Schema\\\\Blueprint))\n#7 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Support\\\\Facades\\\\Facade.php(361): Illuminate\\\\Database\\\\Schema\\\\Builder->table('models', Object(Closure))\n#8 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\database\\\\migrations\\\\2025_08_14_221801_add_model_warranty_to_models_table.php(14): Illuminate\\\\Support\\\\Facades\\\\Facade::__callStatic('table', Array)\n#9 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(507): Illuminate\\\\Database\\\\Migrations\\\\Migration@anonymous->up()\n#10 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(432): Illuminate\\\\Database\\\\Migrations\\\\Migrator->runMethod(Object(Illuminate\\\\Database\\\\MySqlConnection), Object(Illuminate\\\\Database\\\\Migrations\\\\Migration@anonymous), 'up')\n#11 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(441): Illuminate\\\\Database\\\\Migrations\\\\Migrator->Illuminate\\\\Database\\\\Migrations\\\\{closure}()\n#12 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(244): Illuminate\\\\Database\\\\Migrations\\\\Migrator->runMigration(Object(Illuminate\\\\Database\\\\Migrations\\\\Migration@anonymous), 'up')\n#13 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Console\\\\View\\\\Components\\\\Task.php(40): Illuminate\\\\Database\\\\Migrations\\\\Migrator->Illuminate\\\\Database\\\\Migrations\\\\{closure}()\n#14 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(798): Illuminate\\\\Console\\\\View\\\\Components\\\\Task->render('2025_08_14_2218...', Object(Closure))\n#15 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(244): Illuminate\\\\Database\\\\Migrations\\\\Migrator->write('Illuminate\\\\\\\\Cons...', '2025_08_14_2218...', Object(Closure))\n#16 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(211): Illuminate\\\\Database\\\\Migrations\\\\Migrator->runUp('C:\\\\\\\\xampp\\\\\\\\htdocs...', 1, false)\n#17 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(138): Illuminate\\\\Database\\\\Migrations\\\\Migrator->runPending(Array, Array)\n#18 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand.php(117): Illuminate\\\\Database\\\\Migrations\\\\Migrator->run(Array, Array)\n#19 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Migrations\\\\Migrator.php(658): Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand->Illuminate\\\\Database\\\\Console\\\\Migrations\\\\{closure}()\n#20 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand.php(110): Illuminate\\\\Database\\\\Migrations\\\\Migrator->usingConnection(NULL, Object(Closure))\n#21 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand.php(89): Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand->runMigrations()\n#22 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(36): Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand->handle()\n#23 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\Util.php(43): Illuminate\\\\Container\\\\BoundMethod::Illuminate\\\\Container\\\\{closure}()\n#24 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(95): Illuminate\\\\Container\\\\Util::unwrapIfClosure(Object(Closure))\n#25 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\BoundMethod.php(35): Illuminate\\\\Container\\\\BoundMethod::callBoundMethod(Object(Illuminate\\\\Foundation\\\\Application), Array, Object(Closure))\n#26 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Container\\\\Container.php(696): Illuminate\\\\Container\\\\BoundMethod::call(Object(Illuminate\\\\Foundation\\\\Application), Array, Array, NULL)\n#27 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Console\\\\Command.php(213): Illuminate\\\\Container\\\\Container->call(Array)\n#28 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\symfony\\\\console\\\\Command\\\\Command.php(318): Illuminate\\\\Console\\\\Command->execute(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Illuminate\\\\Console\\\\OutputStyle))\n#29 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Console\\\\Command.php(182): Symfony\\\\Component\\\\Console\\\\Command\\\\Command->run(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Illuminate\\\\Console\\\\OutputStyle))\n#30 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\symfony\\\\console\\\\Application.php(1092): Illuminate\\\\Console\\\\Command->run(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#31 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\symfony\\\\console\\\\Application.php(341): Symfony\\\\Component\\\\Console\\\\Application->doRunCommand(Object(Illuminate\\\\Database\\\\Console\\\\Migrations\\\\MigrateCommand), Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#32 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\symfony\\\\console\\\\Application.php(192): Symfony\\\\Component\\\\Console\\\\Application->doRun(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#33 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\vendor\\\\laravel\\\\framework\\\\src\\\\Illuminate\\\\Foundation\\\\Console\\\\Kernel.php(198): Symfony\\\\Component\\\\Console\\\\Application->run(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#34 C:\\\\xampp\\\\htdocs\\\\Snipe-IT-AMS-Group7\\\\snipe-it-master\\\\artisan(33): Illuminate\\\\Foundation\\\\Console\\\\Kernel->handle(Object(Symfony\\\\Component\\\\Console\\\\Input\\\\ArgvInput), Object(Symfony\\\\Component\\\\Console\\\\Output\\\\ConsoleOutput))\n#35 {main}\n\"} \n#32 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#33 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#34 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#35 {main}\n\nNext Illuminate\\Database\\QueryException: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'model_warranty' (Connection: mysql, SQL: alter table `models` add `model_warranty` int null) in C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825\nStack trace:\n#0 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('alter table `mo...', Array, Object(Closure))\n#1 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('alter table `mo...', Array, Object(Closure))\n#2 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('alter table `mo...')\n#3 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))\n#4 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(406): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))\n#5 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('models', Object(Closure))\n#6 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\database\\migrations\\2025_08_14_221801_add_model_warranty_to_models_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)\n#7 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()\n#8 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')\n#9 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()\n#10 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')\n#11 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()\n#12 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_08_14_2218...', Object(Closure))\n#13 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_14_2218...', Object(Closure))\n#14 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 1, false)\n#15 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)\n#16 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)\n#17 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()\n#18 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))\n#19 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()\n#20 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()\n#21 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#22 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))\n#23 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))\n#24 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)\n#25 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)\n#26 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))\n#27 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))\n#28 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#29 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#30 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#31 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#32 C:\\xampp\\htdocs\\Snipe-IT-AMS-Group7\\snipe-it-master\\artisan(33): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))\n#33 {main}  \n", "label": "error", "time": "2025-08-16 08:26:06", "collector": "laravel.log", "is_string": false}]}, "files": {"messages": [{"message": "'\\server.php',", "is_string": true}, {"message": "'\\public\\index.php',", "is_string": true}, {"message": "'\\vendor\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_real.php',", "is_string": true}, {"message": "'\\vendor\\composer\\platform_check.php',", "is_string": true}, {"message": "'\\vendor\\composer\\ClassLoader.php',", "is_string": true}, {"message": "'\\vendor\\composer\\autoload_static.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\deprecation-contracts\\function.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-mbstring\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-normalizer\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-ctype\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-grapheme\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\string\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php83\\bootstrap81.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Resources\\functions\\dump.php',", "is_string": true}, {"message": "'\\vendor\\ralouphie\\getallheaders\\src\\getallheaders.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php80\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-intl-idn\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\react\\promise\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\prompts\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\Resources\\now.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-uuid\\bootstrap80.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Resources\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ramsey\\uuid\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\fakerphp\\faker\\src\\Faker\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\functions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mtdowling\\jmespath.php\\src\\JmesPath.php',", "is_string": true}, {"message": "'\\vendor\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\php-text-template\\src\\Template.php',", "is_string": true}, {"message": "'\\vendor\\phpseclib\\phpseclib\\phpseclib\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\flare-client-php\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\polyfill-php81\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\aws\\aws-sdk-php\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.composer.php',", "is_string": true}, {"message": "'\\vendor\\phpstan\\phpstan\\bootstrap.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php',", "is_string": true}, {"message": "'\\vendor\\psy\\psysh\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\helpers\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions_include.php',", "is_string": true}, {"message": "'\\vendor\\league\\csv\\src\\functions.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\helpers.php',", "is_string": true}, {"message": "'\\vendor\\mockery\\mockery\\library\\Mockery.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Runner\\Version.php',", "is_string": true}, {"message": "'\\vendor\\sebastian\\version\\src\\Version.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Phpunit\\Subscribers\\EnsurePrinterIsRegisteredSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Application\\StartedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Subscriber.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\autoload-php7.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\src\\Compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\namespaced.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\sodium_compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\constants.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat_const.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\php84compat.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\stream-xchacha20.php',", "is_string": true}, {"message": "'\\vendor\\paragonie\\sodium_compat\\lib\\ristretto255.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\autoload.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\InvocationOrder.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\SelfDescribing.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Invocation.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\MockObject.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Interface\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationMocker.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\InvocationStubber.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\MethodNameMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\ParametersMatch.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Stub.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Builder\\Identity.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\Rule\\MethodName.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Framework\\MockObject\\Runtime\\InvocationHandler.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockDisablerPHPUnit10.php',", "is_string": true}, {"message": "'\\vendor\\phpunit\\phpunit\\src\\Event\\Events\\Test\\Lifecycle\\FinishedSubscriber.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\DefaultArgumentRemoverReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\php-mock\\php-mock-phpunit\\classes\\MockObjectProxyReturnTypes100.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Helpers\\functions.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\helpers.php',", "is_string": true}, {"message": "'\\bootstrap\\app.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\Container.php',", "is_string": true}, {"message": "'\\vendor\\psr\\container\\src\\ContainerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\CachesRoutes.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-kernel\\HttpKernelInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\ContextServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\InteractsWithTime.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Http\\Kernel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualAttribute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Tappable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\BindingRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ReflectsClosures.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Events\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Request.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\AcceptHeaderItem.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\FileBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ParameterBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\HeaderUtils.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\InputBag.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ServerBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\CanBePrecognitive.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithContentTypes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithFlashData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Concerns\\InteractsWithInput.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Dumpable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\InteractsWithData.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Arrayable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Carbon.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Date.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Boundaries.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Comparison.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Converter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ToStringFormat.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Creator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\ObjectInitialisation.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\LocalFactory.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Difference.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Macro.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mixin.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\MagicParameter.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Modifiers.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Mutability.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Cast.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Options.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticOptions.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Localization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\StaticLocalization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Rounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalRounding.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Serialization.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Test.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Timestamp.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Units.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Week.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonTimeZone.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonInterval.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\IntervalStep.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonConverterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\FactoryImmutable.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\clock\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\psr\\clock\\src\\ClockInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadEnvironmentVariables.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Env.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Option.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryBuilder.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ServerConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\AdapterInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ReaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\WriterInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\Some.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\EnvConstAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\PutenvAdapter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiReader.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\MultiWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\Adapter\\ImmutableWriter.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\AdapterRepository.php',", "is_string": true}, {"message": "'\\vendor\\vlucas\\phpdotenv\\src\\Repository\\RepositoryInterface.php',", "is_string": true}, {"message": "'\\vendor\\phpoption\\phpoption\\src\\PhpOption\\None.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\config.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Config\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\EnvironmentDetector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\AliasLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\CanBeEscapedWhenCastToString.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Enumerable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Jsonable.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\packages.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php',", "is_string": true}, {"message": "'\\bootstrap\\cache\\services.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasGlobalScopes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasTimestamps.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasUniqueIds.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HidesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\PreventsCircularRecursion.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\HasCollection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Broadcasting\\HasBroadcastChannel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableEntity.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlRoutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\SerializableClosure.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Signers\\Hmac.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Signer.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Signed.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Contracts\\Serializable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FoundationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\AggregateServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\ParallelTestingServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Testing\\Concerns\\TestDatabases.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\DeferrableProvider.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\AbstractCloner.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\ClonerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\HtmlDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\CliDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\AbstractDumper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Dumper\\DataDumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\DumperInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Concerns\\ResolvesDumpSource.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Cloner\\VarCloner.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HigherOrderTapProxy.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\Caster\\ReflectionCaster.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\var-dumper\\VarDumper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Uri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Htmlable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Responsable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Reflector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\NotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\PaginationState.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\Paginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\AbstractCursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pagination\\CursorPaginator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-dompdf\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\eduardokum\\laravel-mail-auto-embed\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\intervention\\image\\src\\Intervention\\Image\\ImageServiceProviderLaravelRecent.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\google-chat\\src\\GoogleChatServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel-notification-channels\\microsoft-teams\\src\\MicrosoftTeamsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Notification.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\ChannelManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Dispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Notifications\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\PassportServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Passport.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Container\\ContextualBindingBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Auth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\slack-notification-channel\\src\\SlackChannelServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\UiServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\LivewireManager.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\EventBus.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\Mechanism.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\FrontendAssets\\FrontendAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendBlade.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\CompileLivewireTags.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ComponentRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\RenderComponent.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\DataStore.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Laravel\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\collision\\src\\Adapters\\Laravel\\CollisionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Adapters\\Laravel\\InsightsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\phpinsights\\src\\Application\\Injectors\\Repositories.php',", "is_string": true}, {"message": "'\\vendor\\nunomaduro\\termwind\\src\\Laravel\\TermwindServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\osa-eg\\laravel-teams-notification\\src\\LaravelTeamsNotificationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\pragmarx\\google2fa-laravel\\src\\ServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\BackupServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Package.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasAssets.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasBladeComponents.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasCommands.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasConfigs.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInertia.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasInstallCommand.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasMigrations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasRoutes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasServiceProviders.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasTranslations.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewComposers.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViews.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\Package\\HasViewSharedData.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\IgnitionServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\IgnitionConfig.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Config\\FileConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\ignition\\src\\Contracts\\ConfigManager.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\error-solutions\\src\\Contracts\\SolutionProviderRepository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Log.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\ParsesLogConfiguration.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Level.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-signal-aware-command\\src\\SignalAwareCommandServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CreatesRegularExpressionRouteConstraints.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\FiltersControllerMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php',", "is_string": true}, {"message": "'\\vendor\\unicodeveloper\\laravel-password\\src\\DumbPasswordServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AppServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\AuthServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\EventServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SettingsServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BladeServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\LivewireServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\MacroServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\SamlServiceProvider.php',", "is_string": true}, {"message": "'\\app\\Providers\\BreadcrumbsServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionResolverInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Renderer\\Listener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\debugbar-routes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUri.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteGroup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\service-contracts\\ResetInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Console\\Application.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Event.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\routes\\web.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\ui\\src\\AuthRouteMethods.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\AboutCommand.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\Command.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Command\\SignalableCommandInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\HasParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithIO.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\InteractsWithSignals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\PromptsForMissingInput.php',", "is_string": true}, {"message": "'\\vendor\\composer\\InstalledVersions.php',", "is_string": true}, {"message": "'\\vendor\\composer\\installed.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\console\\Output\\OutputInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Stringable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Blade.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesAuthorizations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesClasses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesConditionals.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesEchos.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesIncludes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesInjections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJson.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesJs.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesRawPhp.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesStyles.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Concerns\\CompilesUseStatements.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\CompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\LivewireTagPrecompiler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHookRegistry.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireModelingNestedComponents\\SupportWireModelingNestedComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\ComponentHook.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMultipleRootElementDetection\\SupportMultipleRootElementDetection.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\SupportDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestedComponentListeners\\SupportNestedComponentListeners.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportMorphAwareIfStatement\\SupportMorphAwareIfStatement.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Livewire.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAutoInjectedAssets\\SupportAutoInjectedAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportComputed\\SupportLegacyComputedPropertySyntax.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNestingComponents\\SupportNestingComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportScriptsAndAssets\\SupportScriptsAndAssets.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportBladeAttributes\\SupportBladeAttributes.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ComponentAttributeBag.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportConsoleCommands\\SupportConsoleCommands.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\SupportPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\View.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Support\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportReactiveProps\\SupportReactiveProps.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileDownloads\\SupportFileDownloads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\SupportJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportQueryString\\SupportQueryString.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFileUploads\\SupportFileUploads.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTeleporting\\SupportTeleporting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLazyLoading\\SupportLazyLoading.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\SupportFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\SupportAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination\\SupportPagination.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\SupportValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportIsolating\\SupportIsolating.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\SupportRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\SupportStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportNavigate\\SupportNavigate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEntangle\\SupportEntangle.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLocales\\SupportLocales.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportTesting\\SupportTesting.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportModels\\SupportModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\SupportEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportLegacyModels\\SupportLegacyModels.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportWireables\\SupportWireables.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogue.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MessageCatalogueInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\MetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\CatalogueMetadataAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\TranslatorBagInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\LocaleAwareInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\TranslatorStrongType.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\AbstractTranslator.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\TranslatorStrongTypeInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\ArrayLoader.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Loader\\LoaderInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\MessageFormatter\\MessageFormatterMapper.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\MessageFormatter\\MessageFormatterMapperStrongType.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\MessageFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\Formatter\\IntlFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation\\IdentityTranslator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\translation-contracts\\TranslatorTrait.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Lang\\en_US.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\CarbonPeriod.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\lazy\\Carbon\\UnprotectedDatePeriod.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Date.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\DateFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Events\\LocaleUpdated.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Notifications\\EventHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-backup\\src\\Listeners\\EncryptBackupArchive.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\ignition-routes.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\HealthCheckController.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\ExecuteSolutionController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Validation\\ValidatesRequests.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Http\\Controllers\\UpdateConfigController.php',", "is_string": true}, {"message": "'\\app\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Debug\\ExceptionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\ReportableHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\DumpRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\DumpRecorder\\MultiDumpHandler.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\JobRecorder\\JobRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogRecorder.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\QueryRecorder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Serializers\\Native.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\Monitor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Validator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Validation\\Factory.php',", "is_string": true}, {"message": "'\\app\\Providers\\SnipeTranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\TranslationServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\FileLoader.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Loader.php',", "is_string": true}, {"message": "'\\app\\Services\\SnipeTranslator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\NamespacedItemResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\Translator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\PresenceVerifierInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Routing\\UrlGenerator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Optional.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\NullHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\Handler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\HandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\ResettableInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Logger.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\LogRecord.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\JsonSerializableDateTimeImmutable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Context\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\Events\\MessageLogged.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\LogRecorder\\LogMessage.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\URL.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Schema.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ConfigurationUrlParser.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsConcurrencyErrors.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DetectsLostConnections.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\ConnectionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\CompilesJsonPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseTransactionsManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEstablished.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\ConnectionEvent.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Grammars\\Grammar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Asset.php',", "is_string": true}, {"message": "'\\app\\Models\\Depreciable.php',", "is_string": true}, {"message": "'\\app\\Models\\SnipeModel.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\HasUploads.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Factories\\HasFactory.php',", "is_string": true}, {"message": "'\\app\\Models\\Loggable.php',", "is_string": true}, {"message": "'\\app\\Models\\Requestable.php',", "is_string": true}, {"message": "'\\app\\Presenters\\Presentable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletes.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingTrait.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\Injectors\\UniqueInjector.php',", "is_string": true}, {"message": "'\\app\\Http\\Traits\\UniqueUndeletedTrait.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Acceptable.php',", "is_string": true}, {"message": "'\\app\\Models\\Traits\\Searchable.php',", "is_string": true}, {"message": "'\\app\\Models\\CompanyableScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Scope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\SoftDeletingScope.php',", "is_string": true}, {"message": "'\\vendor\\watson\\validating\\src\\ValidatingObserver.php',", "is_string": true}, {"message": "'\\app\\Observers\\AssetObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\User.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Passwords\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\HasApiTokens.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\Notifiable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\HasDatabaseNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Notifications\\RoutesNotifications.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Authenticatable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Authorizable.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\CanResetPassword.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Translation\\HasLocalePreference.php',", "is_string": true}, {"message": "'\\app\\Observers\\UserObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Accessory.php',", "is_string": true}, {"message": "'\\app\\Observers\\AccessoryObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Component.php',", "is_string": true}, {"message": "'\\app\\Observers\\ComponentObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Consumable.php',", "is_string": true}, {"message": "'\\app\\Observers\\ConsumableObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\License.php',", "is_string": true}, {"message": "'\\app\\Observers\\LicenseObserver.php',", "is_string": true}, {"message": "'\\app\\Models\\Setting.php',", "is_string": true}, {"message": "'\\app\\Observers\\SettingObserver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\HandlesAuthorization.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Access\\Gate.php',", "is_string": true}, {"message": "'\\vendor\\nesbot\\carbon\\src\\Carbon\\Unit.php',", "is_string": true}, {"message": "'\\app\\Listeners\\LogListener.php',", "is_string": true}, {"message": "'\\app\\Listeners\\CheckoutableListener.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RateLimiter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\FileStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RetrievesMultipleKeys.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\LockProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cache\\Repository.php',", "is_string": true}, {"message": "'\\vendor\\psr\\simple-cache\\src\\CacheInterface.php',", "is_string": true}, {"message": "'\\routes\\api.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResourceRegistrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\PendingResourceRegistration.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Pluralizer.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Language.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\InflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\GenericLanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\LanguageInflectorFactory.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Rules.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Ruleset.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformations.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\WordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Inflectible.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Transformation.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Pattern.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Patterns.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\English\\Uninflected.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitutions.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Substitution.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Rules\\Word.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\Inflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\CachedWordInflector.php',", "is_string": true}, {"message": "'\\vendor\\doctrine\\inflector\\lib\\Doctrine\\Inflector\\RulesetInflector.php',", "is_string": true}, {"message": "'\\routes\\web\\hardware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ClosureScope.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\serializable-closure\\src\\Support\\ReflectionClosure.php',", "is_string": true}, {"message": "'\\routes\\web\\models.php',", "is_string": true}, {"message": "'\\routes\\web\\accessories.php',", "is_string": true}, {"message": "'\\routes\\web\\licenses.php',", "is_string": true}, {"message": "'\\routes\\web\\locations.php',", "is_string": true}, {"message": "'\\routes\\web\\consumables.php',", "is_string": true}, {"message": "'\\routes\\web\\fields.php',", "is_string": true}, {"message": "'\\routes\\web\\components.php',", "is_string": true}, {"message": "'\\routes\\web\\users.php',", "is_string": true}, {"message": "'\\routes\\web\\kits.php',", "is_string": true}, {"message": "'\\routes\\web.php',", "is_string": true}, {"message": "'\\app\\Livewire\\Importer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Component.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\AuthorizesRequests.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Concerns\\InteractsWithProperties.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportEvents\\HandlesEvents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportRedirects\\HandlesRedirects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportStreaming\\HandlesStreaming.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportAttributes\\HandlesAttributes.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportValidation\\HandlesValidation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportFormObjects\\HandlesFormObjects.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportJsEvaluation\\HandlesJsEvaluation.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportPageComponents\\HandlesPageComponents.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\HandlesDisablingBackButtonCache.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\HealthController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\DashboardController.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\Controller.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bus\\DispatchesJobs.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\GroupsController.php',", "is_string": true}, {"message": "'\\routes\\scim.php',", "is_string": true}, {"message": "'\\vendor\\arietimmerman\\laravel-scim-server\\src\\RouteProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewFinderInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesComponents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesFragments.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLayouts.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesLoops.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesStacks.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesTranslations.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewName.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\App.php',", "is_string": true}, {"message": "'\\resources\\macros\\macros.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormFacade.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\FormBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\Componentable.php',", "is_string": true}, {"message": "'\\vendor\\laravelcollective\\html\\src\\HtmlBuilder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\FileSessionHandler.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Session.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Breadcrumbs.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Manager.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Trail.php',", "is_string": true}, {"message": "'\\vendor\\tabuna\\breadcrumbs\\src\\Registrar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Pipeline\\Pipeline.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\NoSessionStore.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\Concerns\\ExcludesPaths.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\MaintenanceModeManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\FileBasedMaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Foundation\\MaintenanceMode.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SymfonySessionDecorator.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Session\\SessionInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ViewErrorBag.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForSetup.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsWhereDateClauses.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ExplainsQueries.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Query\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Database\\Eloquent\\Builder.php',", "is_string": true}, {"message": "'\\app\\Models\\Company.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\StatementPrepared.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Events\\QueryExecuted.php',", "is_string": true}, {"message": "'\\vendor\\spatie\\laravel-ignition\\src\\Recorders\\QueryRecorder\\Query.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\InteractsWithDictionary.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Queue\\QueueableCollection.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForDebug.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\CreatesUserProviders.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\StatefulGuard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\Guard.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\SupportsBasicAuth.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Auth\\UserProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Hashing\\HashManager.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Hashing\\Hasher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Timebox.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieJar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\QueueingFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Cookie\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Recaller.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\TrimStrings.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\SecurityHeaders.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\PreventBackHistory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php',", "is_string": true}, {"message": "'\\vendor\\fruitcake\\php-cors\\src\\CorsService.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\IpUtils.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\LaravelDebugbar.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DebugBar.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\RequestIdGenerator.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\RequestIdGeneratorInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Storage\\FilesystemStorage.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Storage\\StorageInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PhpInfoCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasDataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\HasXdebugLinks.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\DataCollectorInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\Renderable.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\AbstractLogger.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LoggerTrait.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MessagesAggregateInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\AssetProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\TimeDataCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataFormatter\\DataFormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\FileLinkFormatter.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\error-handler\\ErrorRenderer\\ErrorRendererInterface.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\MemoryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ExceptionsCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LaravelCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\EventCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\SimpleFormatter.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\ViewCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RouteCollector.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\StreamHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractProcessingHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\AbstractHandler.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerTrait.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\ProcessableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Handler\\FormattableHandlerInterface.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Utils.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\LineFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\NormalizerFormatter.php',", "is_string": true}, {"message": "'\\vendor\\monolog\\monolog\\src\\Monolog\\Formatter\\FormatterInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\QueryCollector.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\PDO\\PDOCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataFormatter\\QueryFormatter.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\DataCollector\\ObjectCountCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LivewireCollector.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\MailServiceProvider.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\Bridge\\Symfony\\SymfonyMailCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\LogsCollector.php',", "is_string": true}, {"message": "'\\vendor\\psr\\log\\src\\LogLevel.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\FilesCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\MultiAuthCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\GateCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\JavascriptRenderer.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\Routing.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\Route.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompiler.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\RouteCompilerInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\routing\\CompiledRoute.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\UriValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\ValidatorInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\MethodValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\SchemeValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Matching\\HostValidator.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteParameterBinder.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\RouteMatched.php',", "is_string": true}, {"message": "'\\app\\Http\\Controllers\\SettingsController.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Contracts\\ControllerDispatcher.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\MiddlewareNameResolver.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\SortedMiddleware.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckLocale.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckUserIsActivated.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\CheckForTwoFactor.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php',", "is_string": true}, {"message": "'\\app\\Http\\Middleware\\AssetCountForSidebar.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\Encrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\StringEncrypter.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\Encryption\\DecryptException.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\CookieValuePrefix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php',", "is_string": true}, {"message": "'\\app\\Helpers\\Helper.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\passport\\src\\ApiTokenCookieFactory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php',", "is_string": true}, {"message": "'\\app\\Models\\Statuslabel.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsTo.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\ComparesRelatedModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Concerns\\SupportsDefaultModels.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\DB.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Http.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\Factory.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\uri-template\\src\\UriTemplate.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Utils.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlMultiHandler.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactoryInterface.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\StreamHandler.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Client.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\ClientTrait.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\ClientInterface.php',", "is_string": true}, {"message": "'\\vendor\\psr\\http-client\\src\\ClientInterface.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Cookie\\CookieJar.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Cookie\\CookieJarInterface.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\RequestOptions.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\psr7\\src\\Utils.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\psr7\\src\\Uri.php',", "is_string": true}, {"message": "'\\vendor\\psr\\http-message\\src\\UriInterface.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\psr7\\src\\Request.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\psr7\\src\\MessageTrait.php',", "is_string": true}, {"message": "'\\vendor\\psr\\http-message\\src\\RequestInterface.php',", "is_string": true}, {"message": "'\\vendor\\psr\\http-message\\src\\MessageInterface.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\psr7\\src\\Stream.php',", "is_string": true}, {"message": "'\\vendor\\psr\\http-message\\src\\StreamInterface.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\promises\\src\\Create.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\Request.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\Events\\RequestSending.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\EasyHandle.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\TransferStats.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\ConnectException.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\TransferException.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\GuzzleException.php',", "is_string": true}, {"message": "'\\vendor\\psr\\http-client\\src\\ClientExceptionInterface.php',", "is_string": true}, {"message": "'\\vendor\\psr\\http-client\\src\\NetworkExceptionInterface.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\promises\\src\\RejectedPromise.php',", "is_string": true}, {"message": "'\\vendor\\guzzlehttp\\promises\\src\\PromiseInterface.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\ConnectionException.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\HttpClientException.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\Events\\ConnectionFailed.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\File.php',", "is_string": true}, {"message": "'\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Contracts\\View\\Engine.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\PreparingResponse.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\Response.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\http-foundation\\ResponseHeaderBag.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\ResponseTrait.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\a88d6f4940f290750f4e0163885c2cc2.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteUrlGenerator.php',", "is_string": true}, {"message": "'\\storage\\framework\\views\\80af18e3426ab94c3642180b72280944.php',", "is_string": true}, {"message": "'\\resources\\lang\\en-US\\general.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Mix.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\HtmlString.php',", "is_string": true}, {"message": "'\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Events\\ResponsePrepared.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\SymfonyHttpDriver.php',", "is_string": true}, {"message": "'\\vendor\\php-debugbar\\php-debugbar\\src\\DebugBar\\HttpDriverInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\SessionCollector.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\Ulid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\AbstractUid.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\HashableInterface.php',", "is_string": true}, {"message": "'\\vendor\\symfony\\uid\\TimeBasedUidInterface.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\DataCollector\\RequestCollector.php',", "is_string": true}, {"message": "'\\vendor\\barryvdh\\laravel-debugbar\\src\\Support\\Clockwork\\ClockworkCollector.php',", "is_string": true}], "count": 979}, "auth": {"guards": {"web": "null", "api": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "kMriDdJm63KmKcQH9YpixPdaXtVJ0UvjFus6skCZ", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/_debugbar/assets/javascript?v=1753138890\"\n]", "url": "[]", "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "$2y$10$6ayBYQ6PavFU.qQNHmBCBOh5tx4g0DUZZB6kC.belbMD2Fwmw82ka", "menu_state": "open", "redirect_option": "index", "checkout_to_type": "user", "other_redirect": "model", "back_url": "http://127.0.0.1:8000/hardware/2598", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/setup", "action_name": "setup", "controller_action": "App\\Http\\Controllers\\SettingsController@getSetupIndex", "uri": "GET setup", "controller": "App\\Http\\Controllers\\SettingsController@getSetupIndex<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FSettingsController.php&line=60\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/setup", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FSnipe-IT-AMS-Group7%2Fsnipe-it-master%2Fapp%2FHttp%2FControllers%2FSettingsController.php&line=60\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/SettingsController.php:60-126</a>", "middleware": "web, web", "duration": "10.71s", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1856720328 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1856720328\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-855951433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-855951433\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Microsoft Edge&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1620 characters\">csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ii9iKzdCYk1TTXlTeUVucTRXS1dreXc9PSIsInZhbHVlIjoiUGpnTHowZ3hhM0tUemlCVCthMVJhMmVRRU5XdDJYNHc0QXg1UTRFSHdwT0ZkTndaYkdIM2ZGeHRYc3VBVkRKcmxKQzlVK3g3cXpmQ1BCYjhxTmhGdGJ6MGF2NnpnLzljcGRiTk5INk1qRFhIeGVJUUNGa2dlNFZJenVaN1NRbWE2Sm5JUHRBZ0hZdzBRajJsLy9xQkUzK3hWQlJrc25XcE1KU2pUZmVmdzlnekxqOTFOM2xRNnNlSytQODdwMDk5K0FlOFJRVzJxYmFMckdtUkFnZUZHNmJDUnR2NU5SNm9HeWQ0UlBwNzl2Zz0iLCJtYWMiOiJiYjI1MDZiMDc1NWRhYjBlOTJlNjE3MTMxYjljNWZmNGZkMTA1ODA4NzcxZWIyOWYyY2M1MzA1NjhhZDMxMGY2IiwidGFnIjoiIn0%3D; snipeit_session=0iPZQDR16ejYvrGiFjzYtaXRcle7O2G1ioPfN6Ht; snipeit_passport_token=eyJpdiI6IlVzZWVFNUppb2NTU0tvNWRkVFBLRXc9PSIsInZhbHVlIjoiVXZaaXhWbk1uSmt6QmMvVGxidFRWSDBiT1RCdDBtNjZqc0FscStBZGYvaWViZmlxQXUrdkNyaU00OUpMYk5lSWo4V0I5bldUMVNmQnl2K0xaeUU5alFqUVVNMDhFUFZ0RHEyN0pZK0dxRmdObGxEMXV0SFp6dzg0MlMvajhEQVRrcnMrMUFxQjJCZ3pid0tYWGNqSGZMNnhSMHBxU3l0bDYySnhQZXBORE5zN3FWMDQycGlPbFIyTnFydjQ3cURQczhUN2NvV0FWNHQ1Y0I5UXRwM3dlY05HVlRoYnhWQ2VFR1h0YXNGaG1GVEVxRTZSaDQ1MkhYaEFxSWhPQkFQelgwcnNGZlNURGxZbEl3c3VobUNqWWVsZmloclBUNTFoUExCN0tyQWR2VzQ2dGVZazBkbnY5Qk5EQ3hFeFlFb1oiLCJtYWMiOiI4OWRjNTE1ODAzZmUwZmEyYjI1MzVhZTAyZTdlNGE2OTY2Yzk5YjQ3MDIzZjFjNWM1MjdhNWIyYWU5YjBkOTk3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVqVkwvSG94RVR3UFVwS2lFdkNvNkE9PSIsInZhbHVlIjoicEFjZFBHaXlkNHZHUTFkOHU5VTZKUlA3OWxnQ3ZCTy8xckxRVEVra3FuK1Y3UHJVZXl5dHF2MHRKRTk1emdlQnZsRTZOVEFFV3UxNzBaT1JMeEE3NjR1TjRseVFQVDBIWUxGZ2U2YURWbWQ0QkQrMDJORmFoVmpQUGRzVFBaenEiLCJtYWMiOiIwZGUwNTIxNjEzMzIyNGU4Nzk4MmZhZGMwZTgwY2NkNWM4YTFiYzQ0ZTEzODQ4Yjk1NGVjZjlkYzYzYmQ1YjU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>csrftoken</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>snipeit_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>snipeit_passport_token</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kMriDdJm63KmKcQH9YpixPdaXtVJ0UvjFus6skCZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1992804953 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 16 Aug 2025 08:32:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">01K2S04MENQKFTVP314ZGKNA41</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-clockwork-path</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">_debugbar/clockwork/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992804953\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-993199730 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kMriDdJm63KmKcQH9YpixPdaXtVJ0UvjFus6skCZ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/_debugbar/assets/javascript?v=1753138890</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>backUrl</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$10$6ayBYQ6PavFU.qQNHmBCBOh5tx4g0DUZZB6kC.belbMD2Fwmw82ka</span>\"\n  \"<span class=sf-dump-key>menu_state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">open</span>\"\n  \"<span class=sf-dump-key>redirect_option</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  \"<span class=sf-dump-key>checkout_to_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n  \"<span class=sf-dump-key>other_redirect</span>\" => \"<span class=sf-dump-str title=\"5 characters\">model</span>\"\n  \"<span class=sf-dump-key>back_url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/hardware/2598</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993199730\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/setup", "action_name": "setup", "controller_action": "App\\Http\\Controllers\\SettingsController@getSetupIndex"}, "badge": null}, "clockwork": {"getData": [], "postData": [], "headers": {"host": ["127.0.0.1:8000"], "connection": ["keep-alive"], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["none"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-US,en;q=0.9"], "cookie": ["csrftoken=eRZeQ4hlaHT8dltJRqLktclcixjXegkX; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ii9iKzdCYk1TTXlTeUVucTRXS1dreXc9PSIsInZhbHVlIjoiUGpnTHowZ3hhM0tUemlCVCthMVJhMmVRRU5XdDJYNHc0QXg1UTRFSHdwT0ZkTndaYkdIM2ZGeHRYc3VBVkRKcmxKQzlVK3g3cXpmQ1BCYjhxTmhGdGJ6MGF2NnpnLzljcGRiTk5INk1qRFhIeGVJUUNGa2dlNFZJenVaN1NRbWE2Sm5JUHRBZ0hZdzBRajJsLy9xQkUzK3hWQlJrc25XcE1KU2pUZmVmdzlnekxqOTFOM2xRNnNlSytQODdwMDk5K0FlOFJRVzJxYmFMckdtUkFnZUZHNmJDUnR2NU5SNm9HeWQ0UlBwNzl2Zz0iLCJtYWMiOiJiYjI1MDZiMDc1NWRhYjBlOTJlNjE3MTMxYjljNWZmNGZkMTA1ODA4NzcxZWIyOWYyY2M1MzA1NjhhZDMxMGY2IiwidGFnIjoiIn0%3D; snipeit_session=0iPZQDR16ejYvrGiFjzYtaXRcle7O2G1ioPfN6Ht; snipeit_passport_token=eyJpdiI6IlVzZWVFNUppb2NTU0tvNWRkVFBLRXc9PSIsInZhbHVlIjoiVXZaaXhWbk1uSmt6QmMvVGxidFRWSDBiT1RCdDBtNjZqc0FscStBZGYvaWViZmlxQXUrdkNyaU00OUpMYk5lSWo4V0I5bldUMVNmQnl2K0xaeUU5alFqUVVNMDhFUFZ0RHEyN0pZK0dxRmdObGxEMXV0SFp6dzg0MlMvajhEQVRrcnMrMUFxQjJCZ3pid0tYWGNqSGZMNnhSMHBxU3l0bDYySnhQZXBORE5zN3FWMDQycGlPbFIyTnFydjQ3cURQczhUN2NvV0FWNHQ1Y0I5UXRwM3dlY05HVlRoYnhWQ2VFR1h0YXNGaG1GVEVxRTZSaDQ1MkhYaEFxSWhPQkFQelgwcnNGZlNURGxZbEl3c3VobUNqWWVsZmloclBUNTFoUExCN0tyQWR2VzQ2dGVZazBkbnY5Qk5EQ3hFeFlFb1oiLCJtYWMiOiI4OWRjNTE1ODAzZmUwZmEyYjI1MzVhZTAyZTdlNGE2OTY2Yzk5YjQ3MDIzZjFjNWM1MjdhNWIyYWU5YjBkOTk3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVqVkwvSG94RVR3UFVwS2lFdkNvNkE9PSIsInZhbHVlIjoicEFjZFBHaXlkNHZHUTFkOHU5VTZKUlA3OWxnQ3ZCTy8xckxRVEVra3FuK1Y3UHJVZXl5dHF2MHRKRTk1emdlQnZsRTZOVEFFV3UxNzBaT1JMeEE3NjR1TjRseVFQVDBIWUxGZ2U2YURWbWQ0QkQrMDJORmFoVmpQUGRzVFBaenEiLCJtYWMiOiIwZGUwNTIxNjEzMzIyNGU4Nzk4MmZhZGMwZTgwY2NkNWM4YTFiYzQ0ZTEzODQ4Yjk1NGVjZjlkYzYzYmQ1YjU2IiwidGFnIjoiIn0%3D"]}, "cookies": {"csrftoken": null, "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": null, "snipeit_session": null, "snipeit_passport_token": null, "XSRF-TOKEN": "kMriDdJm63KmKcQH9YpixPdaXtVJ0UvjFus6skCZ"}, "uri": "/setup", "method": "GET", "responseStatus": 200, "sessionData": {"_token": "kMriDdJm63KmKcQH9YpixPdaXtVJ0UvjFus6skCZ", "_flash": {"old": [], "new": []}, "_previous": {"url": "http://127.0.0.1:8000/_debugbar/assets/javascript?v=1753138890"}, "url": [], "backUrl": "http://127.0.0.1:8000/login", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 1, "password_hash_web": "$2y$10$6ayBYQ6PavFU.qQNHmBCBOh5tx4g0DUZZB6kC.belbMD2Fwmw82ka", "menu_state": "open", "redirect_option": "index", "checkout_to_type": "user", "other_redirect": "model", "back_url": "http://127.0.0.1:8000/hardware/2598", "PHPDEBUGBAR_STACK_DATA": []}}}