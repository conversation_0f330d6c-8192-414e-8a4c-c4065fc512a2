<?php return array(
    'root' => array(
        'name' => 'grokability/snipe-it',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '031a78dfbf668aabed17c07e117d97a5042565a9',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'alek13/slack' => array(
            'pretty_version' => '2.3.0',
            'version' => '*******',
            'reference' => '9b50bae7a8149a3afb96cb11674972e3adcdf103',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alek13/slack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arietimmerman/laravel-scim-server' => array(
            'pretty_version' => 'dev-laravel_11_compatibility',
            'version' => 'dev-laravel_11_compatibility',
            'reference' => '6c771799090bfe04dcee94a1dc9f82870aed4dbe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arietimmerman/laravel-scim-server',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aws/aws-crt-php' => array(
            'pretty_version' => 'v1.2.7',
            'version' => '1.2.7.0',
            'reference' => 'd71d9906c7bb63a28295447ba12e74723bd3730e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-crt-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aws/aws-sdk-php' => array(
            'pretty_version' => '3.351.7',
            'version' => '3.351.7.0',
            'reference' => '9506d7fdb3cb84f8d7b175c594db9993264814be',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aws/aws-sdk-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bacon/bacon-qr-code' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'reference' => '8674e51bb65af933a5ffaf1c308a660387c35c22',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'barryvdh/laravel-debugbar' => array(
            'pretty_version' => 'v3.16.0',
            'version' => '3.16.0.0',
            'reference' => 'f265cf5e38577d42311f1a90d619bcd3740bea23',
            'type' => 'library',
            'install_path' => __DIR__ . '/../barryvdh/laravel-debugbar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'barryvdh/laravel-dompdf' => array(
            'pretty_version' => 'v2.2.0',
            'version' => '*******',
            'reference' => 'c96f90c97666cebec154ca1ffb67afed372114d8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../barryvdh/laravel-dompdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.12.3',
            'version' => '0.12.3.0',
            'reference' => '866551da34e9a618e64a819ee1e01c20d8a588ba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '99f76ffa36cce3b70a4a6abce41dba15ca2e84cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'clue/ndjson-react' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '392dc165fce93b5bb5c637b67e59619223c931b0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clue/ndjson-react',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'cmgmyr/phploc' => array(
            'pretty_version' => '8.0.6',
            'version' => '8.0.6.0',
            'reference' => '5d785f8fc8b891483cdbee3fb25f2b348c50c03f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cmgmyr/phploc',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/pcre' => array(
            'pretty_version' => '3.3.2',
            'version' => '3.3.2.0',
            'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e',
            'type' => 'library',
            'install_path' => __DIR__ . '/./pcre',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/xdebug-handler' => array(
            'pretty_version' => '3.0.5',
            'version' => '3.0.5.0',
            'reference' => '6c1925561632e83d60a44492e0b344cf48ab85ef',
            'type' => 'library',
            'install_path' => __DIR__ . '/./xdebug-handler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.6',
            'version' => '1.0.6.0',
            'reference' => '8dfd07c6d2cf31c8da90c53b83c026c7696dda90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dealerdirect/phpcodesniffer-composer-installer' => array(
            'pretty_version' => 'v1.1.2',
            'version' => '1.1.2.0',
            'reference' => 'e9cf5e4bbf7eeaf9ef5db34938942602838fc2b1',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../dealerdirect/phpcodesniffer-composer-installer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'defuse/php-encryption' => array(
            'pretty_version' => 'v2.4.0',
            'version' => '2.4.0.0',
            'reference' => 'f53396c2d34225064647a05ca76c1da9d99e5828',
            'type' => 'library',
            'install_path' => __DIR__ . '/../defuse/php-encryption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.3',
            'version' => '3.0.3.0',
            'reference' => 'a23a2bf4f31d3518f3ecb38660c95715dfead60f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/cache' => array(
            'pretty_version' => '1.13.0',
            'version' => '1.13.0.0',
            'reference' => '56cd022adb5514472cb144c087393c1821911d09',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/dbal' => array(
            'pretty_version' => '3.10.0',
            'version' => '3.10.0.0',
            'reference' => '1cf840d696373ea0d58ad0a8875c0fadcfc67214',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/dbal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/event-manager' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b680156fa328f1dfd874fd48c7026c41570b9c6e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/event-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/instantiator' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'reference' => '0a0fa9780f5d4e507415a065172d26a98d02047b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/instantiator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/dompdf' => array(
            'pretty_version' => 'v2.0.8',
            'version' => '2.0.8.0',
            'reference' => 'c20247574601700e1f7c8dab39310fca1964dc52',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '3.4.0.0',
            'reference' => '8c784d071debd117328803d86b2097615b457500',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'eduardokum/laravel-mail-auto-embed' => array(
            'pretty_version' => '2.12',
            'version' => '2.12.0.0',
            'reference' => 'ba5438aff4b4a78c4af101acaf69cad6577f2775',
            'type' => 'library',
            'install_path' => __DIR__ . '/../eduardokum/laravel-mail-auto-embed',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd42c8731f0624ad6bdc8d3e5e9a4524f68801cfa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'enshrined/svg-sanitize' => array(
            'pretty_version' => '0.16.0',
            'version' => '0.16.0.0',
            'reference' => '239e257605e2141265b429e40987b2ee51bba4b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../enshrined/svg-sanitize',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'erusev/parsedown' => array(
            'pretty_version' => '1.7.4',
            'version' => '1.7.4.0',
            'reference' => 'cb17b6477dfff935958ba01325f2e8a2bfa6dab3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../erusev/parsedown',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'evenement/evenement' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '0a16b0d71ab13284339abb99d9d2bd813640efbc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../evenement/evenement',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.18.0',
            'version' => '4.18.0.0',
            'reference' => 'cb56001e54359df7ae76dc522d08845dc741621b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fakerphp/faker' => array(
            'pretty_version' => 'v1.24.1',
            'version' => '1.24.1.0',
            'reference' => 'e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fakerphp/faker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fidry/cpu-core-counter' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'reference' => '8520451a140d3f46ac33042715115e290cf5785f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fidry/cpu-core-counter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.18.3',
            'version' => '2.18.3.0',
            'reference' => '59a123a3d459c5a23055802237cb317f609867e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.11.1',
            'version' => '6.11.1.0',
            'reference' => 'd1e91ecf8c598d073d0995afa8cd5c75c6e19e66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'friendsofphp/php-cs-fixer' => array(
            'pretty_version' => 'v3.84.0',
            'version' => '3.84.0.0',
            'reference' => '38dad0767bf2a9b516b976852200ae722fe984ca',
            'type' => 'application',
            'install_path' => __DIR__ . '/../friendsofphp/php-cs-fixer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'fruitcake/php-cors' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '3d158f36e7875e2f040f37bc0573956240a5a38b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fruitcake/php-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '*******',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'grogy/php-parallel-lint' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'grokability/snipe-it' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '031a78dfbf668aabed17c07e117d97a5042565a9',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '*******',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '*******',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.4',
            'version' => '1.0.4.0',
            'reference' => '30e286560c137526eccd4ce21b2de477ab0676d2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.1.1',
            'version' => '2.1.1.0',
            'reference' => 'f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'iamcal/sql-parser' => array(
            'pretty_version' => 'v0.5',
            'version' => '0.5.0.0',
            'reference' => '644fd994de3b54e5d833aecf406150aa3b66ca88',
            'type' => 'library',
            'install_path' => __DIR__ . '/../iamcal/sql-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/collections' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/concurrency' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/conditionable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/macroable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/process' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/testing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v11.45.1',
            ),
        ),
        'intervention/image' => array(
            'pretty_version' => '2.7.2',
            'version' => '2.7.2.0',
            'reference' => '04be355f8d6734c826045d02a1079ad658322dad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jakub-onderka/php-parallel-lint' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'javiereguiluz/easyslugger' => array(
            'pretty_version' => 'v1.0.0',
            'version' => '1.0.0.0',
            'reference' => '11524a3fd70e3f0c98043755a0ffa228f2529211',
            'type' => 'library',
            'install_path' => __DIR__ . '/../javiereguiluz/easyslugger',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'justinrainbow/json-schema' => array(
            'pretty_version' => '6.4.2',
            'version' => '6.4.2.0',
            'reference' => 'ce1fd2d47799bb60668643bc6220f6278a4c1d02',
            'type' => 'library',
            'install_path' => __DIR__ . '/../justinrainbow/json-schema',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'larastan/larastan' => array(
            'pretty_version' => 'v2.11.2',
            'version' => '2.11.2.0',
            'reference' => '1aae902a5851c03dc1a58cbd9010a0c3ef8def63',
            'type' => 'phpstan-extension',
            'install_path' => __DIR__ . '/../larastan/larastan',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'laravel-notification-channels/google-chat' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '39ec6d130044066c46b891e5620220be5fa166d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel-notification-channels/google-chat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel-notification-channels/microsoft-teams' => array(
            'pretty_version' => '1.3.0',
            'version' => '1.3.0.0',
            'reference' => 'e19c029525b54728fe978afa6bead6c8f33a842f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel-notification-channels/microsoft-teams',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v11.45.1',
            'version' => '11.45.1.0',
            'reference' => 'b09ba32795b8e71df10856a2694706663984a239',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/helpers' => array(
            'pretty_version' => 'v1.7.2',
            'version' => '1.7.2.0',
            'reference' => '672d79d5b5f65dc821e57783fa11f22c4d762d70',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/helpers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/passport' => array(
            'pretty_version' => 'v12.4.2',
            'version' => '12.4.2.0',
            'reference' => '65a885607b62d361aedaeb10a946bc6b5a954262',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/passport',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.3.6',
            'version' => '0.3.6.0',
            'reference' => '86a8b692e8661d0fb308cec64f3d176821323077',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'b352cf0534aa1ae6b4d825d1e762e35d43f8a841',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/slack-notification-channel' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '642677a57490eebccb7e9fb666f5a5379c6e3459',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/slack-notification-channel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/socialite' => array(
            'pretty_version' => 'v5.23.0',
            'version' => '5.23.0.0',
            'reference' => 'e9e0fc83b9d8d71c8385a5da20e5b95ca6234cf5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/socialite',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/tinker' => array(
            'pretty_version' => 'v2.10.1',
            'version' => '2.10.1.0',
            'reference' => '22177cc71807d38f2810c6204d8f7183d88a57d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/tinker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/ui' => array(
            'pretty_version' => 'v4.6.1',
            'version' => '4.6.1.0',
            'reference' => '7d6ffa38d79f19c9b3e70a751a9af845e8f41d88',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/ui',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravelcollective/html' => array(
            'pretty_version' => '6.x-dev',
            'version' => '6.9999999.9999999.9999999-dev',
            'reference' => '944b7029c207914ecec437c0141b7a0e32d9e1a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravelcollective/html',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lcobucci/clock' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'lcobucci/jwt' => array(
            'pretty_version' => '5.5.0',
            'version' => '5.5.0.0',
            'reference' => 'a835af59b030d3f2967725697cf88300f579088e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lcobucci/jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => '10732241927d3971d28e7ea7b5712721fa2296ca',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/container' => array(
            'pretty_version' => '5.1.0',
            'version' => '5.1.0.0',
            'reference' => '041c52d266763887fff2256fb5dc9392d808f8f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/container',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'league/csv' => array(
            'pretty_version' => '9.24.1',
            'version' => '9.24.1.0',
            'reference' => 'e0221a3f16aa2a823047d59fab5809d552e29bc8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/csv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/event' => array(
            'pretty_version' => '2.3.0',
            'version' => '*******',
            'reference' => '062ebb450efbe9a09bc2478e89b7c933875b0935',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/event',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.30.0',
            'version' => '3.30.0.0',
            'reference' => '2203e3151755d874bb2943649dae1eb8533ac93e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-aws-s3-v3' => array(
            'pretty_version' => '3.29.0',
            'version' => '3.29.0.0',
            'reference' => 'c6ff6d4606e48249b63f269eba7fabdb584e76a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-aws-s3-v3',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.30.0',
            'version' => '3.30.0.0',
            'reference' => '6691915f77c7fb69adfb87dcd550052dc184ee10',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.16.0',
            'version' => '1.16.0.0',
            'reference' => '2d6702ff215bf922936ccc1ad31007edc76451b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/oauth1-client' => array(
            'pretty_version' => 'v1.11.0',
            'version' => '1.11.0.0',
            'reference' => 'f9c94b088837eb1aae1ad7c4f23eb65cc6993055',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth1-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/oauth2-server' => array(
            'pretty_version' => '8.5.5',
            'version' => '8.5.5.0',
            'reference' => 'cc8778350f905667e796b3c2364a9d3bd7a73518',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth2-server',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/oauth2server' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'league/uri' => array(
            'pretty_version' => '7.5.1',
            'version' => '7.5.1.0',
            'reference' => '81fb5145d2644324614cc532b28efd0215bda430',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/uri-interfaces' => array(
            'pretty_version' => '7.5.0',
            'version' => '7.5.0.0',
            'reference' => '08cfc6c4f3d811584fb09c37e2849e6a7f9b0742',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri-interfaces',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'livewire/livewire' => array(
            'pretty_version' => 'v3.6.4',
            'version' => '3.6.4.0',
            'reference' => 'ef04be759da41b14d2d129e670533180a44987dc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../livewire/livewire',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lncd/oauth2' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'malkusch/php-mock' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'marc-mabe/php-enum' => array(
            'pretty_version' => 'v4.7.1',
            'version' => '4.7.1.0',
            'reference' => '7159809e5cfa041dca28e61f7f7ae58063aae8ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../marc-mabe/php-enum',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.10.0',
            'version' => '2.10.0.0',
            'reference' => 'fcf91eb64359852f00d921887b219479b4f21251',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maximebf/debugbar' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v2.2.4',
            ),
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.12',
            'version' => '1.6.12.0',
            'reference' => '1f4efdd7d3beafe9807b08156dfcb176d18f1699',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.9.0',
            'version' => '3.9.0.0',
            'reference' => '10d85740180ecba7896c87e06a166e0c95a0e3b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.8.0',
            'version' => '2.8.0.0',
            'reference' => 'a2a865e05d5f420b50cc2f85bb78d565db12a6bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.3',
            'version' => '1.13.3.0',
            'reference' => 'faed855a7b5f4d4637717c2b3863e277116beb36',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'neitanod/forceutf8' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'c1fbe70bfb5ad41b8ec5785056b0e308b40d4831',
            'type' => 'library',
            'install_path' => __DIR__ . '/../neitanod/forceutf8',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '3.10.1',
            'version' => '3.10.1.0',
            'reference' => '1fd1935b2d90aef2f093c5e35f7ae1257c448d00',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.3.2',
            'version' => '1.3.2.0',
            'reference' => 'da801d52f0354f70a638673c4a0f04e16529431d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.7',
            'version' => '4.0.7.0',
            'reference' => 'e67c4061eb40b9c113b218214e42cb5a0dda28f2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.6.0',
            'version' => '5.6.0.0',
            'reference' => '221b0d0fdf1369c71047ad1d18bb5880017bbc56',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/collision' => array(
            'pretty_version' => 'v8.5.0',
            'version' => '8.5.0.0',
            'reference' => 'f5c101b929c958e849a633283adff296ed5f38f5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/collision',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/phpinsights' => array(
            'pretty_version' => 'v2.13.1',
            'version' => '2.13.1.0',
            'reference' => '77572bb0d3a6fbbd36aa000a619fd5c89b10d3df',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/phpinsights',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v2.3.1',
            'version' => '2.3.1.0',
            'reference' => 'dfa08f390e509967a15c22493dc0bac5733d9123',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nyholm/psr7' => array(
            'pretty_version' => '1.8.2',
            'version' => '1.8.2.0',
            'reference' => 'a71f2b11690f4b24d099d6b16690a90ae14fc6f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nyholm/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'okvpn/clock-lts' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => '5e7dc00d23a7d65e8ed2b0ff7a4dcf7860c05a14',
            'type' => 'library',
            'install_path' => __DIR__ . '/../okvpn/clock-lts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'onelogin/php-saml' => array(
            'pretty_version' => '3.8.0',
            'version' => '3.8.0.0',
            'reference' => '03bd22f5e028a8aa3b5fec9864bb8984a55df899',
            'type' => 'library',
            'install_path' => __DIR__ . '/../onelogin/php-saml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'onnov/detect-encoding' => array(
            'pretty_version' => 'v2.0.0',
            'version' => '2.0.0.0',
            'reference' => '6a8159ac3e6178ae043244b9d66a9b2701121e07',
            'type' => 'library',
            'install_path' => __DIR__ . '/../onnov/detect-encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'orno/di' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '~2.0',
            ),
        ),
        'osa-eg/laravel-teams-notification' => array(
            'pretty_version' => 'v2.1.4',
            'version' => '2.1.4.0',
            'reference' => '1b36cdfe422881b810f31eba4acf61f72080124e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../osa-eg/laravel-teams-notification',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.7.0',
            'version' => '2.7.0.0',
            'reference' => '52a0d99e69f56b9ec27ace92ba56897fe6993105',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/sodium_compat' => array(
            'pretty_version' => 'v1.21.1',
            'version' => '1.21.1.0',
            'reference' => 'bb312875dcdd20680419564fe42ba1d9564b9e37',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/sodium_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phenx/php-font-lib' => array(
            'pretty_version' => '0.5.6',
            'version' => '0.5.6.0',
            'reference' => 'a1681e9793040740a405ac5b189275059e2a9863',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-font-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phenx/php-svg-lib' => array(
            'pretty_version' => '0.5.4',
            'version' => '0.5.4.0',
            'reference' => '46b25da81613a9cf43c83b2a8c2c1bdab27df691',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-svg-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-debugbar/php-debugbar' => array(
            'pretty_version' => 'v2.2.4',
            'version' => '2.2.4.0',
            'reference' => '3146d04671f51f69ffec2a4207ac3bdcf13a9f35',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-debugbar/php-debugbar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/message-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-mock/php-mock' => array(
            'pretty_version' => '2.6.1',
            'version' => '2.6.1.0',
            'reference' => 'c7b6789056dfc3c45389cabbe9930dc33aeb2bf0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-mock/php-mock',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'php-mock/php-mock-integration' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '8ceb860f343a143af604efeb66a7a124381cc52e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-mock/php-mock-integration',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'php-mock/php-mock-phpunit' => array(
            'pretty_version' => '2.13.0',
            'version' => '2.13.0.0',
            'reference' => '498e5e25ee7824570332581304c2bb7e37d75e80',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-mock/php-mock-phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'php-parallel-lint/php-parallel-lint' => array(
            'pretty_version' => 'v1.4.0',
            'version' => '1.4.0.0',
            'reference' => '6db563514f27e19595a19f45a4bf757b6401194e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-parallel-lint/php-parallel-lint',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpdocumentor/reflection-common' => array(
            'pretty_version' => '2.2.0',
            'version' => '*******',
            'reference' => '1d01c49d4ed62f25aa84a747ad35d5a16924662b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpdocumentor/reflection-docblock' => array(
            'pretty_version' => '5.6.2',
            'version' => '5.6.2.0',
            'reference' => '92dde6a5919e34835c506ac8c523ef095a95ed62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-docblock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpdocumentor/type-resolver' => array(
            'pretty_version' => '1.10.0',
            'version' => '1.10.0.0',
            'reference' => '679e3ce485b99e84c775d28e2e96fade9a7fb50a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/type-resolver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '3.0.46',
            'version' => '3.0.46.0',
            'reference' => '56483a7de62a6c2a6635e42e93b8a9e25d4f0ec6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpspec/prophecy' => array(
            'pretty_version' => 'v1.22.0',
            'version' => '1.22.0.0',
            'reference' => '35f1adb388946d92e6edab2aa2cb2b60e132ebd5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpspec/prophecy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpstan/phpdoc-parser' => array(
            'pretty_version' => '2.2.0',
            'version' => '*******',
            'reference' => 'b9e61a61e39e02dd90944e9115241c7f7e76bfd8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpdoc-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpstan/phpstan' => array(
            'pretty_version' => '1.12.28',
            'version' => '1.12.28.0',
            'reference' => 'fcf8b71aeab4e1a1131d1783cef97b23a51b87a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpstan',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '10.1.16',
            'version' => '10.1.16.0',
            'reference' => '7e308268858ed6baedc8704a304727d20bc07c77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '4.1.0',
            'version' => '4.1.0.0',
            'reference' => 'a95037b6d9e608ba092da1b23931e537cadc3c3c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => 'f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '0c7b06ff49e3d5072f057eb1fa59258bf287a748',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '6.0.0',
            'version' => '6.0.0.0',
            'reference' => 'e2a2d67966e740530f4a3343fe2e030ffdc1161d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '10.5.48',
            'version' => '10.5.48.0',
            'reference' => '6e0a2bc39f6fae7617989d690d76c48e6d2eb541',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'pragmarx/google2fa' => array(
            'pretty_version' => 'v8.0.3',
            'version' => '8.0.3.0',
            'reference' => '6f8d87ebd5afbf7790bde1ffc7579c7c705e0fad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa-laravel' => array(
            'pretty_version' => 'v1.4.1',
            'version' => '1.4.1.0',
            'reference' => 'f9014fd7ea36a1f7fffa233109cf59b209469647',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa-laravel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa-qrcode' => array(
            'pretty_version' => 'v1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'fd5ff0531a48b193a659309cc5fb882c14dbd03f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa-qrcode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
                1 => '^1.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
                1 => '3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.9',
            'version' => '0.12.9.0',
            'reference' => '1b801844becfe648985372cb4b12ad6840245ace',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '344572933ad0181accbf4ba763e85a0306a8c5e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.9.0',
            'version' => '4.9.0.0',
            'reference' => '4e0e23cc785f0724a0e838279a9eb03f28b092a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/cache' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => 'd47c472b64aa5608225f47965a484b75c7817d5b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/cache',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/child-process' => array(
            'pretty_version' => 'v0.6.6',
            'version' => '0.6.6.0',
            'reference' => '1721e2b93d89b745664353b9cfc8f155ba8a6159',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/child-process',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/dns' => array(
            'pretty_version' => 'v1.13.0',
            'version' => '1.13.0.0',
            'reference' => 'eb8ae001b5a455665c89c1df97f6fb682f8fb0f5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/dns',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/event-loop' => array(
            'pretty_version' => 'v1.5.0',
            'version' => '1.5.0.0',
            'reference' => 'bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/event-loop',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/promise' => array(
            'pretty_version' => 'v3.2.0',
            'version' => '3.2.0.0',
            'reference' => '8a164643313c71354582dc850b42b33fa12a4b63',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/promise',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/socket' => array(
            'pretty_version' => 'v1.16.0',
            'version' => '1.16.0.0',
            'reference' => '23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/socket',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'react/stream' => array(
            'pretty_version' => 'v1.4.0',
            'version' => '1.4.0.0',
            'reference' => '1e5b0acb8fe55143b5b426817155190eb6f5b18d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/stream',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.9.0',
            ),
        ),
        'robrichards/xmlseclibs' => array(
            'pretty_version' => '3.1.3',
            'version' => '3.1.3.0',
            'reference' => '2bdfd742624d739dfadbd415f00181b4a77aaf07',
            'type' => 'library',
            'install_path' => __DIR__ . '/../robrichards/xmlseclibs',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rollbar/rollbar' => array(
            'pretty_version' => 'v4.1.3',
            'version' => '4.1.3.0',
            'reference' => '99eb7bd2ea1ef3be4a6e693b022911b1db1582bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rollbar/rollbar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rollbar/rollbar-laravel' => array(
            'pretty_version' => 'v8.1.3',
            'version' => '8.1.3.0',
            'reference' => 'd622878343a62f40c55f4157693f424c8ee69939',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rollbar/rollbar-laravel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.9.0',
            'version' => '8.9.0.0',
            'reference' => 'd8e916507b88e389e26d4ab03c904a082aa66bb9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'c34583b87e7b7a8055bf6c450c2c77ce32a24084',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a81fee9eef0b7a76af11d121767abc44c104e503',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '5e3a687f7d8ae33fb362c5c0743794bbb2420a1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => 'a18251eb0b7a2dcd2f7aa3d6078b18545ef0558e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '68ff824baeae169ec9f2137158ee529584553799',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '5.1.1',
            'version' => '5.1.1.0',
            'reference' => 'c41e007b4b62af48218231d6c2275e4c9b975b2e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '6.1.0',
            'version' => '6.1.0.0',
            'reference' => '8074dbcd93529b357029f5cc5058fd3e43666984',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '5.1.2',
            'version' => '5.1.2.0',
            'reference' => '955288482d97c19a372d3f31006ab3f37da47adf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'reference' => '987bafff24ecc4c9ac418cab1145b96dd6e9cbd9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => '856e7f6a75a84e339195d48c556f23be2ebf75d0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '5.0.0',
            'version' => '5.0.0.0',
            'reference' => '202d0e344a580d7f7d04b3fafce6933e59dae906',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '24ed13d98130f0e7122df55d06c5c4942a577957',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '5.0.0',
            'version' => '5.0.0.0',
            'reference' => '05909fb5bc7df4c52992396d0116aed689f93712',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/type' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => '462699a16464c3944eefc02ebdd77882bd3925bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'c51fa83a5d8f43f1402e3f32a005e6262244ef17',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'slevomat/coding-standard' => array(
            'pretty_version' => '8.20.0',
            'version' => '8.20.0.0',
            'reference' => 'b4f9f02edd4e6a586777f0cabe8d05574323f3eb',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../slevomat/coding-standard',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/backtrace' => array(
            'pretty_version' => '1.7.4',
            'version' => '1.7.4.0',
            'reference' => 'cd37a49fce7137359ac30ecc44ef3e16404cccbe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/backtrace',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/db-dumper' => array(
            'pretty_version' => '3.8.0',
            'version' => '3.8.0.0',
            'reference' => '91e1fd4dc000aefc9753cda2da37069fc996baee',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/db-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/error-solutions' => array(
            'pretty_version' => '1.1.3',
            'version' => '*******',
            'reference' => 'e495d7178ca524f2dd0fe6a1d99a1e608e1c9936',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/error-solutions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/flare-client-php' => array(
            'pretty_version' => '1.10.1',
            'version' => '1.10.1.0',
            'reference' => 'bf1716eb98bd689451b071548ae9e70738dce62f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/flare-client-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/ignition' => array(
            'pretty_version' => '1.15.1',
            'version' => '1.15.1.0',
            'reference' => '31f314153020aee5af3537e507fef892ffbf8c85',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/ignition',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-backup' => array(
            'pretty_version' => '8.8.2',
            'version' => '8.8.2.0',
            'reference' => '5b672713283703a74c629ccd67b1d77eb57e24b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-backup',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-html' => array(
            'pretty_version' => '3.12.0',
            'version' => '3.12.0.0',
            'reference' => '3655f335609d853f51e431698179ddfe05851126',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-html',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-ignition' => array(
            'pretty_version' => '2.9.1',
            'version' => '2.9.1.0',
            'reference' => '1baee07216d6748ebd3a65ba97381b051838707a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-ignition',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-package-tools' => array(
            'pretty_version' => '1.92.7',
            'version' => '1.92.7.0',
            'reference' => 'f09a799850b1ed765103a4f0b4355006360c49a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-package-tools',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-signal-aware-command' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '8e8a226ed7fb45302294878ef339e75ffa9a878d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-signal-aware-command',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/once' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'spatie/temporary-directory' => array(
            'pretty_version' => '2.3.0',
            'version' => '*******',
            'reference' => '580eddfe9a0a41a902cac6eeb8f066b42e65a32b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/temporary-directory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'squizlabs/php_codesniffer' => array(
            'pretty_version' => '3.13.2',
            'version' => '3.13.2.0',
            'reference' => '5b5e3821314f947dd040c70f7992a64eac89025c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../squizlabs/php_codesniffer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/cache' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => 'a7c6caa9d6113cebfb3020b427bcb021ebfdfc9e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/cache-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '5d68a57d66910405e5c0b63d6f0af941e66fc868',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/cache-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/clock' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'b81435fbd6648ea425d1ee96a2d8e68f4ceacd24',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '9e27aecde8f506ba0fd1d9989620c04a87697101',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v4.4.44',
            'version' => '4.4.44.0',
            'reference' => 'bd0a6737e48de45b4b0b7b6fc98c78404ddceaed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dom-crawler' => array(
            'pretty_version' => 'v4.4.45',
            'version' => '4.4.45.0',
            'reference' => '4b8daf6c56801e6d664224261cb100b73edc78a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dom-crawler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '35b55b166f6752d6aaf21aa042fc5ed280fce235',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '497f73ac996a598c92409b44ac43b6690c4f666d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '59eb412e93815df44f05f342958efa9f46b1e586',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'b8dce482de9d7c9fe2891155035a7248ab5c7fdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'ec2344cf77a48253bbca6939aa3d2477773ea63d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '4403d87a2c16f33345dca93407a8714ee8c05a64',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/http-client-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '75d7043853a42837e68111812f4d964b01e5101c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/http-client-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '3.0',
            ),
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '23dd60256610c86a3414575b70c596e5deff6ed9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '1644879a66e4aa29c36fe33dfa6c54b450ce1831',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => 'b5db5105b290bdbea5ab27b89c69effcf1cb3368',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/options-resolver' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'afb9a8038025e5dbc657378bfab9198d75f10fca',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/options-resolver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '9614ac4d8061dc257ecc64cba1b140873dce8ad3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-uuid' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '21533be36c24be3f4b1669c4725c7d1d2bab4ae2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '40c295f2deb408d5e9d2d32b8ba1dd61e36f05af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/psr-http-message-bridge' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '03f2f72319e7acaf2a9f6fcbe30ef17eec51594f',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/psr-http-message-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '8e213820c5fea844ecea29203d2a308019007c15',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/stopwatch' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => '5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/stopwatch',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'f3570b8c61ca887a9e2938e85cb6458515d2b125',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '241d5ac4910d256660238a7ecf250deba4c73063',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'df210c7a2573f1913b2d17cc95f90f53a73d8f7d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/uid' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => 'a69f69f3159b852651a6bf45a9fdd149520525bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/uid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'reference' => '6e209fbe5f5a7b6043baba46fe5735a4b85d0d42',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'reference' => 'c9a1168891b5aaadfd6332ef44393330b3498c4c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'tabuna/breadcrumbs' => array(
            'pretty_version' => '4.3.0',
            'version' => '4.3.0.0',
            'reference' => 'd3b21ea43c7c157fd279d684b1f591df9d25a78b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tabuna/breadcrumbs',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tecnickcom/tc-lib-barcode' => array(
            'pretty_version' => '1.18.4',
            'version' => '1.18.4.0',
            'reference' => 'cd81392e6e1e57e0f6ff8519b1edbc11d8e47a44',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tecnickcom/tc-lib-barcode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tecnickcom/tc-lib-color' => array(
            'pretty_version' => '1.14.39',
            'version' => '1.14.39.0',
            'reference' => 'f7a414e7ddbdcd98105506ca1eecc68d4820fb89',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tecnickcom/tc-lib-color',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tecnickcom/tcpdf' => array(
            'pretty_version' => '6.10.0',
            'version' => '6.10.0.0',
            'reference' => 'ca5b6de294512145db96bcbc94e61696599c391d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tecnickcom/tcpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => 'v2.2.7',
            'version' => '2.2.7.0',
            'reference' => '83ee6f38df0a63106a9e4536e3060458b74ccedb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tmilos/lexer' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'e7885595614759f1da2ff79b66e3fb26d7f875fa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tmilos/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tmilos/scim-filter-parser' => array(
            'pretty_version' => '1.3.0',
            'version' => '1.3.0.0',
            'reference' => 'cfd9ba1f33e1e15adcab2481bffd74cb9fb35704',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tmilos/scim-filter-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tmilos/scim-schema' => array(
            'pretty_version' => '0.1',
            'version' => '0.1.0.0',
            'reference' => 'bb871e667b33080b4cd36d7a9b2ac2cdbf796062',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tmilos/scim-schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tmilos/value' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '9e78ad9c026b14cacec1a27552ee0ada9d7d1c06',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tmilos/value',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'unicodeveloper/laravel-password' => array(
            'pretty_version' => '1.0.4',
            'version' => '1.0.4.0',
            'reference' => '806e345ae992e0adf38c4cfa32063d7d7c9d189a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../unicodeveloper/laravel-password',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.2',
            'version' => '5.6.2.0',
            'reference' => '24ac4c74f91ee2c193fa1aaa5c249cb0822809af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'watson/validating' => array(
            'pretty_version' => '8.3.0',
            'version' => '8.3.0.0',
            'reference' => '5f24d15727e69857c723aa7bb4c9399931f37827',
            'type' => 'library',
            'install_path' => __DIR__ . '/../watson/validating',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
